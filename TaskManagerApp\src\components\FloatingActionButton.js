import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

const FloatingActionButton = ({ onAddTask, onAISuggest, onSortTasks }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;
  const { theme } = useTheme();

  const toggleMenu = () => {
    const toValue = isExpanded ? 0 : 1;
    
    Animated.spring(animation, {
      toValue,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
    
    setIsExpanded(!isExpanded);
  };

  const handleAction = (action) => {
    toggleMenu();
    setTimeout(() => {
      action();
    }, 150);
  };

  const mainButtonRotation = animation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  const actionButtonScale = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const actionButtonOpacity = animation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0, 1],
  });

  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      bottom: theme.spacing.lg,
      right: theme.spacing.lg,
      alignItems: 'center',
    },
    overlay: {
      position: 'absolute',
      top: -height,
      left: -width,
      width: width * 2,
      height: height * 2,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 1,
    },
    mainButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.colors.fabBackground,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 8,
      zIndex: 3,
    },
    mainButtonText: {
      color: theme.colors.fabText,
      fontSize: 24,
      fontWeight: 'bold',
    },
    actionButton: {
      position: 'absolute',
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.card,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      zIndex: 2,
    },
    actionButtonText: {
      fontSize: 20,
    },
    actionLabel: {
      position: 'absolute',
      right: 60,
      backgroundColor: theme.colors.card,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    actionLabelText: {
      color: theme.colors.text,
      fontSize: theme.fontSize.sm,
      fontWeight: '500',
    },
    addButton: {
      bottom: 80,
    },
    aiButton: {
      bottom: 140,
    },
    sortButton: {
      bottom: 200,
    },
  });

  const actions = [
    {
      key: 'add',
      icon: '➕',
      label: 'Add Task',
      onPress: () => handleAction(onAddTask),
      style: styles.addButton,
    },
    {
      key: 'ai',
      icon: '🤖',
      label: 'AI Suggest',
      onPress: () => handleAction(onAISuggest),
      style: styles.aiButton,
    },
    {
      key: 'sort',
      icon: '📊',
      label: 'Sort Tasks',
      onPress: () => handleAction(onSortTasks),
      style: styles.sortButton,
    },
  ];

  return (
    <View style={styles.container}>
      {isExpanded && (
        <TouchableOpacity
          style={styles.overlay}
          onPress={toggleMenu}
          activeOpacity={1}
        />
      )}

      {actions.map((action) => (
        <Animated.View
          key={action.key}
          style={[
            action.style,
            {
              transform: [{ scale: actionButtonScale }],
              opacity: actionButtonOpacity,
            },
          ]}
        >
          <TouchableOpacity
            style={styles.actionButton}
            onPress={action.onPress}
            activeOpacity={0.8}
          >
            <Text style={styles.actionButtonText}>{action.icon}</Text>
          </TouchableOpacity>
          
          <Animated.View
            style={[
              styles.actionLabel,
              {
                opacity: actionButtonOpacity,
              },
            ]}
          >
            <Text style={styles.actionLabelText}>{action.label}</Text>
          </Animated.View>
        </Animated.View>
      ))}

      <TouchableOpacity
        style={styles.mainButton}
        onPress={toggleMenu}
        activeOpacity={0.8}
      >
        <Animated.Text
          style={[
            styles.mainButtonText,
            {
              transform: [{ rotate: mainButtonRotation }],
            },
          ]}
        >
          +
        </Animated.Text>
      </TouchableOpacity>
    </View>
  );
};

export default FloatingActionButton;
