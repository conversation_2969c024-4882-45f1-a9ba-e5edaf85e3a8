import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { useTask } from '../contexts/TaskContext';
import { useTheme } from '../contexts/ThemeContext';

const TaskItem = ({ task, onPress }) => {
  const { toggleTaskStatus, removeTask } = useTask();
  const { theme } = useTheme();

  const handleToggleStatus = async () => {
    const { error } = await toggleTaskStatus(task.id, task.status);
    if (error) {
      Alert.alert('Error', error.message);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const { error } = await removeTask(task.id);
            if (error) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const getPriorityColor = () => {
    switch (task.priority) {
      case 'high':
        return theme.colors.priorityHigh;
      case 'medium':
        return theme.colors.priorityMedium;
      case 'low':
        return theme.colors.priorityLow;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusColor = () => {
    return task.status === 'completed' 
      ? theme.colors.taskCompleted 
      : theme.colors.taskPending;
  };

  const formatDueDate = (dateString) => {
    if (!dateString) return null;
    
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return { text: `Overdue by ${Math.abs(diffDays)} day(s)`, isOverdue: true };
    } else if (diffDays === 0) {
      return { text: 'Due today', isToday: true };
    } else if (diffDays === 1) {
      return { text: 'Due tomorrow', isTomorrow: true };
    } else {
      return { text: `Due in ${diffDays} day(s)`, isFuture: true };
    }
  };

  const dueDateInfo = formatDueDate(task.due_date);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderLeftWidth: 4,
      borderLeftColor: getPriorityColor(),
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm,
    },
    titleContainer: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    title: {
      fontSize: theme.fontSize.md,
      fontWeight: '600',
      color: theme.colors.text,
      textDecorationLine: task.status === 'completed' ? 'line-through' : 'none',
      opacity: task.status === 'completed' ? 0.7 : 1,
    },
    description: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
      opacity: task.status === 'completed' ? 0.7 : 1,
    },
    statusButton: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    statusButtonCompleted: {
      backgroundColor: theme.colors.taskCompleted,
      borderColor: theme.colors.taskCompleted,
    },
    statusButtonPending: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.taskPending,
    },
    checkmark: {
      color: theme.colors.fabText,
      fontSize: 16,
      fontWeight: 'bold',
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.sm,
    },
    priorityBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: getPriorityColor() + '20',
    },
    priorityText: {
      fontSize: theme.fontSize.xs,
      fontWeight: '600',
      color: getPriorityColor(),
      textTransform: 'uppercase',
    },
    dueDateContainer: {
      flex: 1,
      alignItems: 'center',
    },
    dueDateText: {
      fontSize: theme.fontSize.xs,
      fontWeight: '500',
    },
    actions: {
      flexDirection: 'row',
    },
    actionButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      marginLeft: theme.spacing.xs,
    },
    actionText: {
      fontSize: theme.fontSize.xs,
      fontWeight: '600',
    },
    editText: {
      color: theme.colors.primary,
    },
    deleteText: {
      color: theme.colors.error,
    },
  });

  const getDueDateTextColor = () => {
    if (!dueDateInfo) return theme.colors.textSecondary;
    if (dueDateInfo.isOverdue) return theme.colors.error;
    if (dueDateInfo.isToday) return theme.colors.warning;
    if (dueDateInfo.isTomorrow) return theme.colors.primary;
    return theme.colors.textSecondary;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{task.title}</Text>
          {task.description && (
            <Text style={styles.description} numberOfLines={2}>
              {task.description}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={[
            styles.statusButton,
            task.status === 'completed' 
              ? styles.statusButtonCompleted 
              : styles.statusButtonPending
          ]}
          onPress={handleToggleStatus}
        >
          {task.status === 'completed' && (
            <Text style={styles.checkmark}>✓</Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <View style={styles.priorityBadge}>
          <Text style={styles.priorityText}>{task.priority}</Text>
        </View>

        <View style={styles.dueDateContainer}>
          {dueDateInfo && (
            <Text style={[styles.dueDateText, { color: getDueDateTextColor() }]}>
              {dueDateInfo.text}
            </Text>
          )}
        </View>

        <View style={styles.actions}>
          <TouchableOpacity style={styles.actionButton} onPress={onPress}>
            <Text style={[styles.actionText, styles.editText]}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <Text style={[styles.actionText, styles.deleteText]}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default TaskItem;
