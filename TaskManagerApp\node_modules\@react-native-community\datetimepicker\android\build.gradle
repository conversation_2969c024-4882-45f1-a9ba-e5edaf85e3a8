buildscript {
    repositories {
        google()
        mavenCentral()
    }
}

def getExtOrIntegerDefault(name) {
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeDateTimePicker_' + name]).toInteger()
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
if (isNewArchitectureEnabled()) {
  apply plugin: "com.facebook.react"
}


android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
      namespace "com.reactcommunity.rndatetimepicker"
    }

    compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion getExtOrIntegerDefault('minSdkVersion')
        targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
    }

    sourceSets.main {
        java {
            if (!isNewArchitectureEnabled()) {
                srcDirs += 'src/paper/java'
            }
        }
    }
    kotlinOptions {
        jvmTarget = '17'
    }
}

repositories {
    google()
    mavenLocal()
    mavenCentral()
}

dependencies {
    //noinspection GradleDynamicVersion
    implementation 'com.facebook.react:react-native:+'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.core:core-ktx:1.13.1'
}
