{"version": 3, "sources": ["index.tsx"], "names": ["React", "useContext", "useEffect", "useLayoutEffect", "useMemo", "useRef", "Platform", "findNodeHandle", "isTestEnv", "GestureHandlerRootViewContext", "useAnimatedGesture", "attachHandlers", "needsToReattach", "dropHandlers", "useWebEventHandlers", "Wrap", "AnimatedWrap", "useDetectorUpdater", "useViewRefHandler", "useMountReactions", "propagateDetectorConfig", "props", "gesture", "keysToPropagate", "key", "value", "undefined", "g", "toGestureArray", "config", "GestureDetector", "rootViewContext", "__DEV__", "OS", "Error", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "shouldUseReanimated", "some", "webEventHandlersRef", "state", "firstRender", "viewRef", "previousViewTag", "forceRebuildReanimatedEvent", "current", "preparedGesture", "attachedGestures", "animatedEventHandler", "animatedHandlers", "isMounted", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "needsToRebuildReanimatedEvent", "viewTag", "children"], "mappings": "AAAA;AACA,OAAOA,KAAP,IACEC,UADF,EAEEC,SAFF,EAGEC,eAHF,EAIEC,OAJF,EAKEC,MALF,QAMO,OANP;AAOA,SAASC,QAAT,QAAyB,cAAzB;AACA,OAAOC,cAAP,MAA2B,yBAA3B;AAIA,SAASC,SAAT,QAA0B,gBAA1B;AAEA,OAAOC,6BAAP,MAA0C,wCAA1C;AAEA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,mBAAT,QAAoC,SAApC;AACA,SAASC,IAAT,EAAeC,YAAf,QAAmC,QAAnC;AACA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AACA,SAASC,iBAAT,QAAkC,qBAAlC;;AAEA,SAASC,uBAAT,CACEC,KADF,EAEEC,OAFF,EAGE;AACA,QAAMC,eAA+C,GAAG,CACtD,YADsD,EAEtD,mBAFsD,EAGtD,aAHsD,CAAxD;;AAMA,OAAK,MAAMC,GAAX,IAAkBD,eAAlB,EAAmC;AACjC,UAAME,KAAK,GAAGJ,KAAK,CAACG,GAAD,CAAnB;;AACA,QAAIC,KAAK,KAAKC,SAAd,EAAyB;AACvB;AACD;;AAED,SAAK,MAAMC,CAAX,IAAgBL,OAAO,CAACM,cAAR,EAAhB,EAA0C;AACxC,YAAMC,MAAM,GAAGF,CAAC,CAACE,MAAjB;AACAA,MAAAA,MAAM,CAACL,GAAD,CAAN,GAAcC,KAAd;AACD;AACF;AACF;;AA+BD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,GAAIT,KAAD,IAAiC;AAC9D,QAAMU,eAAe,GAAG9B,UAAU,CAACQ,6BAAD,CAAlC;;AACA,MAAIuB,OAAO,IAAI,CAACD,eAAZ,IAA+B,CAACvB,SAAS,EAAzC,IAA+CF,QAAQ,CAAC2B,EAAT,KAAgB,KAAnE,EAA0E;AACxE,UAAM,IAAIC,KAAJ,CACJ,wNADI,CAAN;AAGD,GAN6D,CAQ9D;;;AACA,QAAMC,aAAa,GAAGd,KAAK,CAACC,OAA5B;AACAF,EAAAA,uBAAuB,CAACC,KAAD,EAAQc,aAAR,CAAvB;AAEA,QAAMC,gBAAgB,GAAGhC,OAAO,CAC9B,MAAM+B,aAAa,CAACP,cAAd,EADwB,EAE9B,CAACO,aAAD,CAF8B,CAAhC;AAIA,QAAME,mBAAmB,GAAGD,gBAAgB,CAACE,IAAjB,CACzBX,CAAD,IAAOA,CAAC,CAACU,mBADiB,CAA5B;AAIA,QAAME,mBAAmB,GAAGzB,mBAAmB,EAA/C,CApB8D,CAqB9D;;AACA,QAAM0B,KAAK,GAAGnC,MAAM,CAAuB;AACzCoC,IAAAA,WAAW,EAAE,IAD4B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCC,IAAAA,eAAe,EAAE,CAAC,CAHuB;AAIzCC,IAAAA,2BAA2B,EAAE;AAJY,GAAvB,CAAN,CAKXC,OALH;AAOA,QAAMC,eAAe,GAAG9C,KAAK,CAACK,MAAN,CAAmC;AACzD0C,IAAAA,gBAAgB,EAAE,EADuC;AAEzDC,IAAAA,oBAAoB,EAAE,IAFmC;AAGzDC,IAAAA,gBAAgB,EAAE,IAHuC;AAIzDZ,IAAAA,mBAAmB,EAAEA,mBAJoC;AAKzDa,IAAAA,SAAS,EAAE;AAL8C,GAAnC,EAMrBL,OANH;AAQA,QAAMM,sBAAsB,GAAGlC,kBAAkB,CAC/CuB,KAD+C,EAE/CM,eAF+C,EAG/CV,gBAH+C,EAI/CD,aAJ+C,EAK/CI,mBAL+C,CAAjD;AAQA,QAAMa,UAAU,GAAGlC,iBAAiB,CAACsB,KAAD,EAAQW,sBAAR,CAApC,CA7C8D,CA+C9D;AACA;;AACA,QAAME,6BAA6B,GACjCb,KAAK,CAACC,WAAN,IACAD,KAAK,CAACI,2BADN,IAEAhC,eAAe,CAACkC,eAAD,EAAkBV,gBAAlB,CAHjB;AAIAI,EAAAA,KAAK,CAACI,2BAAN,GAAoC,KAApC;AAEAlC,EAAAA,kBAAkB,CAACoC,eAAD,EAAkBO,6BAAlB,CAAlB;AAEAlD,EAAAA,eAAe,CAAC,MAAM;AACpB,UAAMmD,OAAO,GAAG/C,cAAc,CAACiC,KAAK,CAACE,OAAP,CAA9B;AACAI,IAAAA,eAAe,CAACI,SAAhB,GAA4B,IAA5B;AAEAvC,IAAAA,cAAc,CAAC;AACbmC,MAAAA,eADa;AAEbX,MAAAA,aAFa;AAGbC,MAAAA,gBAHa;AAIbG,MAAAA,mBAJa;AAKbe,MAAAA;AALa,KAAD,CAAd;AAQA,WAAO,MAAM;AACXR,MAAAA,eAAe,CAACI,SAAhB,GAA4B,KAA5B;AACArC,MAAAA,YAAY,CAACiC,eAAD,CAAZ;AACD,KAHD;AAID,GAhBc,EAgBZ,EAhBY,CAAf;AAkBA5C,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIsC,KAAK,CAACC,WAAV,EAAuB;AACrBD,MAAAA,KAAK,CAACC,WAAN,GAAoB,KAApB;AACD,KAFD,MAEO;AACLU,MAAAA,sBAAsB;AACvB;AACF,GANQ,EAMN,CAAC9B,KAAD,CANM,CAAT;AAQAF,EAAAA,iBAAiB,CAACgC,sBAAD,EAAyBL,eAAzB,CAAjB;;AAEA,MAAIT,mBAAJ,EAAyB;AACvB,wBACE,oBAAC,YAAD;AACE,MAAA,GAAG,EAAEe,UADP;AAEE,MAAA,qBAAqB,EAAEN,eAAe,CAACE;AAFzC,OAGG3B,KAAK,CAACkC,QAHT,CADF;AAOD,GARD,MAQO;AACL,wBAAO,oBAAC,IAAD;AAAM,MAAA,GAAG,EAAEH;AAAX,OAAwB/B,KAAK,CAACkC,QAA9B,CAAP;AACD;AACF,CAhGM", "sourcesContent": ["/* eslint-disable react/no-unused-prop-types */\nimport React, {\n  useContext,\n  useEffect,\n  useLayoutEffect,\n  useMemo,\n  useRef,\n} from 'react';\nimport { Platform } from 'react-native';\nimport findNodeHandle from '../../../findNodeHandle';\nimport { GestureType } from '../gesture';\nimport { UserSelect, TouchAction } from '../../gestureHandlerCommon';\nimport { ComposedGesture } from '../gestureComposition';\nimport { isTestEnv } from '../../../utils';\n\nimport GestureHandlerRootViewContext from '../../../GestureHandlerRootViewContext';\nimport { AttachedGestureState, GestureDetectorState } from './types';\nimport { useAnimatedGesture } from './useAnimatedGesture';\nimport { attachHandlers } from './attachHandlers';\nimport { needsToReattach } from './needsToReattach';\nimport { dropHandlers } from './dropHandlers';\nimport { useWebEventHandlers } from './utils';\nimport { Wrap, AnimatedWrap } from './Wrap';\nimport { useDetectorUpdater } from './useDetectorUpdater';\nimport { useViewRefHandler } from './useViewRefHandler';\nimport { useMountReactions } from './useMountReactions';\n\nfunction propagateDetectorConfig(\n  props: GestureDetectorProps,\n  gesture: ComposedGesture | GestureType\n) {\n  const keysToPropagate: (keyof GestureDetectorProps)[] = [\n    'userSelect',\n    'enableContextMenu',\n    'touchAction',\n  ];\n\n  for (const key of keysToPropagate) {\n    const value = props[key];\n    if (value === undefined) {\n      continue;\n    }\n\n    for (const g of gesture.toGestureArray()) {\n      const config = g.config as { [key: string]: unknown };\n      config[key] = value;\n    }\n  }\n}\n\ninterface GestureDetectorProps {\n  children?: React.ReactNode;\n  /**\n   * A gesture object containing the configuration and callbacks.\n   * Can be any of:\n   * - base gestures (`Tap`, `Pan`, ...)\n   * - `ComposedGesture` (`Race`, `Simultaneous`, `Exclusive`)\n   */\n  gesture: ComposedGesture | GestureType;\n  /**\n   * #### Web only\n   * This parameter allows to specify which `userSelect` property should be applied to underlying view.\n   * Possible values are `\"none\" | \"auto\" | \"text\"`. Default value is set to `\"none\"`.\n   */\n  userSelect?: UserSelect;\n  /**\n   * #### Web only\n   * Specifies whether context menu should be enabled after clicking on underlying view with right mouse button.\n   * Default value is set to `false`.\n   */\n  enableContextMenu?: boolean;\n  /**\n   * #### Web only\n   * This parameter allows to specify which `touchAction` property should be applied to underlying view.\n   * Supports all CSS touch-action values (e.g. `\"none\"`, `\"pan-y\"`). Default value is set to `\"none\"`.\n   */\n  touchAction?: TouchAction;\n}\n\n/**\n * `GestureDetector` is responsible for creating and updating native gesture handlers based on the config of provided gesture.\n *\n * ### Props\n * - `gesture`\n * - `userSelect` (**Web only**)\n * - `enableContextMenu` (**Web only**)\n * - `touchAction` (**Web only**)\n *\n * ### Remarks\n * - Gesture Detector will use first native view in its subtree to recognize gestures, however if this view is used only to group its children it may get automatically collapsed.\n * - Using the same instance of a gesture across multiple Gesture Detectors is not possible.\n *\n * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/gesture-detector\n */\nexport const GestureDetector = (props: GestureDetectorProps) => {\n  const rootViewContext = useContext(GestureHandlerRootViewContext);\n  if (__DEV__ && !rootViewContext && !isTestEnv() && Platform.OS !== 'web') {\n    throw new Error(\n      'GestureDetector must be used as a descendant of GestureHandlerRootView. Otherwise the gestures will not be recognized. See https://docs.swmansion.com/react-native-gesture-handler/docs/installation for more details.'\n    );\n  }\n\n  // Gesture config should be wrapped with useMemo to prevent unnecessary re-renders\n  const gestureConfig = props.gesture;\n  propagateDetectorConfig(props, gestureConfig);\n\n  const gesturesToAttach = useMemo(\n    () => gestureConfig.toGestureArray(),\n    [gestureConfig]\n  );\n  const shouldUseReanimated = gesturesToAttach.some(\n    (g) => g.shouldUseReanimated\n  );\n\n  const webEventHandlersRef = useWebEventHandlers();\n  // Store state in ref to prevent unnecessary renders\n  const state = useRef<GestureDetectorState>({\n    firstRender: true,\n    viewRef: null,\n    previousViewTag: -1,\n    forceRebuildReanimatedEvent: false,\n  }).current;\n\n  const preparedGesture = React.useRef<AttachedGestureState>({\n    attachedGestures: [],\n    animatedEventHandler: null,\n    animatedHandlers: null,\n    shouldUseReanimated: shouldUseReanimated,\n    isMounted: false,\n  }).current;\n\n  const updateAttachedGestures = useDetectorUpdater(\n    state,\n    preparedGesture,\n    gesturesToAttach,\n    gestureConfig,\n    webEventHandlersRef\n  );\n\n  const refHandler = useViewRefHandler(state, updateAttachedGestures);\n\n  // Reanimated event should be rebuilt only when gestures are reattached, otherwise\n  // config update will be enough as all necessary items are stored in shared values anyway\n  const needsToRebuildReanimatedEvent =\n    state.firstRender ||\n    state.forceRebuildReanimatedEvent ||\n    needsToReattach(preparedGesture, gesturesToAttach);\n  state.forceRebuildReanimatedEvent = false;\n\n  useAnimatedGesture(preparedGesture, needsToRebuildReanimatedEvent);\n\n  useLayoutEffect(() => {\n    const viewTag = findNodeHandle(state.viewRef) as number;\n    preparedGesture.isMounted = true;\n\n    attachHandlers({\n      preparedGesture,\n      gestureConfig,\n      gesturesToAttach,\n      webEventHandlersRef,\n      viewTag,\n    });\n\n    return () => {\n      preparedGesture.isMounted = false;\n      dropHandlers(preparedGesture);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (state.firstRender) {\n      state.firstRender = false;\n    } else {\n      updateAttachedGestures();\n    }\n  }, [props]);\n\n  useMountReactions(updateAttachedGestures, preparedGesture);\n\n  if (shouldUseReanimated) {\n    return (\n      <AnimatedWrap\n        ref={refHandler}\n        onGestureHandlerEvent={preparedGesture.animatedEventHandler}>\n        {props.children}\n      </AnimatedWrap>\n    );\n  } else {\n    return <Wrap ref={refHandler}>{props.children}</Wrap>;\n  }\n};\n"]}