{"version": 3, "sources": ["GestureHandler.ts"], "names": ["Hammer", "findNodeHandle", "State", "EventMap", "NodeManager", "ghQueueMicrotask", "gestureInstances", "Gesture<PERSON>andler", "id", "name", "gestureInstance", "isNative", "isDiscrete", "shouldEnableGestureOnSetup", "Error", "constructor", "UNDETERMINED", "Array", "isArray", "config", "waitFor", "gesture", "removePendingGesture", "clearSelfAsPending", "hammer", "stop", "destroy", "x", "y", "rect", "view", "getBoundingClientRect", "pointerInside", "left", "right", "top", "bottom", "nativeEvent", "onGestureHandlerEvent", "onGestureHandlerStateChange", "propsRef", "current", "event", "transformEventData", "invokeNullableMethod", "lastSentState", "state", "get", "enable", "recognizer", "inputData", "enabled", "isGestureRunning", "hasGestureFailed", "options", "maxPointers", "_stillWaiting", "_getPendingGestures", "length", "hasCustomActivationCriteria", "deltaRotation", "initialRotation", "rotation", "success", "failed", "isGestureEnabledForEvent", "getConfig", "simulateCancelEvent", "params", "getHammerConfig", "set", "onWaitingEnded", "_gesture", "pendingGestures", "addPendingGesture", "_config", "_recognizer", "_event", "NativeGestureClass", "updateHasCustomActivationCriteria", "updateGestureConfig", "props", "ensureConfig", "sync", "getState", "type", "eventType", "numberOfPointers", "changedTouch", "changedPointers", "isPointInView", "clientX", "clientY", "previousState", "oldState", "transformNativeEvent", "handlerTag", "target", "ref", "undefined", "timeStamp", "Date", "now", "cancelPendingGestures", "Object", "values", "cancelEvent", "notifyPendingGestures", "onGestureEnded", "forceInvalidate", "sendEvent", "INPUT_CANCEL", "isFinal", "onRawEvent", "<PERSON><PERSON><PERSON><PERSON>", "shouldUseTouchEvents", "simultaneousHandlers", "some", "handler", "<PERSON><PERSON><PERSON><PERSON>", "SUPPORTS_TOUCH", "window", "Manager", "inputClass", "TouchInput", "add", "on", "ev", "setTimeout", "setupEvents", "onStart", "onGestureActivated", "deltaX", "deltaY", "__initialX", "__initialY", "onSuccess", "stillWaiting", "filter", "pointers", "minPointers", "_inputData", "minDist", "minDistSq", "minVelocity", "minVelocitySq", "maxDist", "maxDistSq", "asArray", "map", "<PERSON><PERSON><PERSON><PERSON>", "v", "configProps", "for<PERSON>ach", "prop", "Number", "NaN", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "index", "key", "value", "entries", "nativeValue", "setValue"], "mappings": ";;AAAA;;AACA;AACA,OAAOA,MAAP,MAAmB,gBAAnB;AACA,SAASC,cAAT,QAA+B,cAA/B;AAEA,SAASC,KAAT,QAAsB,UAAtB;AACA,SAASC,QAAT,QAAyB,aAAzB;AACA,OAAO,KAAKC,WAAZ,MAA6B,eAA7B;AACA,SAASC,gBAAT,QAAiC,qBAAjC,C,CAEA;;AA2BA,IAAIC,gBAAgB,GAAG,CAAvB;;AAEA,MAAeC,cAAf,CAA8B;AAsBtB,MAAFC,EAAE,GAAG;AACP,WAAQ,GAAE,KAAKC,IAAK,GAAE,KAAKC,eAAgB,EAA3C;AACD,GAxB2B,CA0B5B;AACA;;;AACY,MAARC,QAAQ,GAAG;AACb,WAAO,KAAP;AACD;;AAEa,MAAVC,UAAU,GAAG;AACf,WAAO,KAAP;AACD;;AAE6B,MAA1BC,0BAA0B,GAAY;AACxC,UAAM,IAAIC,KAAJ,CAAU,yDAAV,CAAN;AACD;;AAEDC,EAAAA,WAAW,GAAG;AAAA;;AAAA,8CAtCY,KAsCZ;;AAAA,kCArCe,IAqCf;;AAAA;;AAAA,8CAnCe,KAmCf;;AAAA,oCAlC2B,IAkC3B;;AAAA,6CAjC6B,IAiC7B;;AAAA;;AAAA;;AAAA,oCA9Ba,EA8Bb;;AAAA,2CA7BmBb,KAAK,CAACc,YA6BzB;;AAAA,6CA5BkC,EA4BlC;;AAAA,sCA3BYd,KAAK,CAACc,YA2BlB;;AAAA,2CA1BwB,IA0BxB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,gDAmCO,MAAM;AACzB,UAAIC,KAAK,CAACC,OAAN,CAAc,KAAKC,MAAL,CAAYC,OAA1B,CAAJ,EAAwC;AACtC,aAAK,MAAMC,OAAX,IAAsB,KAAKF,MAAL,CAAYC,OAAlC,EAA2C;AACzCC,UAAAA,OAAO,CAACC,oBAAR,CAA6B,KAAKd,EAAlC;AACD;AACF;AACF,KAzCa;;AAAA,qCA8DJ,MAAM;AACd,WAAKe,kBAAL;;AAEA,UAAI,KAAKC,MAAT,EAAiB;AACf,aAAKA,MAAL,CAAYC,IAAZ,CAAiB,KAAjB;AACA,aAAKD,MAAL,CAAYE,OAAZ;AACD;;AACD,WAAKF,MAAL,GAAc,IAAd;AACD,KAtEa;;AAAA,2CAwEE,CAAC;AAAEG,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAAD,KAAwC;AACtD;AACA,YAAMC,IAAI,GAAG,KAAKC,IAAL,CAAWC,qBAAX,EAAb;AACA,YAAMC,aAAa,GACjBL,CAAC,IAAIE,IAAI,CAACI,IAAV,IAAkBN,CAAC,IAAIE,IAAI,CAACK,KAA5B,IAAqCN,CAAC,IAAIC,IAAI,CAACM,GAA/C,IAAsDP,CAAC,IAAIC,IAAI,CAACO,MADlE;AAEA,aAAOJ,aAAP;AACD,KA9Ea;;AAAA,uCAiIDK,WAAD,IAAiC;AAC3C,YAAM;AAAEC,QAAAA,qBAAF;AAAyBC,QAAAA;AAAzB,UACJ,KAAKC,QAAL,CAAcC,OADhB;AAGA,YAAMC,KAAK,GAAG,KAAKC,kBAAL,CAAwBN,WAAxB,CAAd;AAEAO,MAAAA,oBAAoB,CAACN,qBAAD,EAAwBI,KAAxB,CAApB;;AACA,UAAI,KAAKG,aAAL,KAAuBH,KAAK,CAACL,WAAN,CAAkBS,KAA7C,EAAoD;AAClD,aAAKD,aAAL,GAAqBH,KAAK,CAACL,WAAN,CAAkBS,KAAvC;AACAF,QAAAA,oBAAoB,CAACL,2BAAD,EAA8BG,KAA9B,CAApB;AACD;AACF,KA5Ia;;AAAA,kCAwTP,MAAM;AACX,YAAMrB,OAAO,GAAG,KAAKG,MAAL,CAAauB,GAAb,CAAiB,KAAKtC,IAAtB,CAAhB;AACA,UAAI,CAACY,OAAL,EAAc;;AAEd,YAAM2B,MAAM,GAAG,CAACC,UAAD,EAAkBC,SAAlB,KAAqC;AAClD,YAAI,CAAC,KAAK/B,MAAL,CAAYgC,OAAjB,EAA0B;AACxB,eAAKC,gBAAL,GAAwB,KAAxB;AACA,eAAKC,gBAAL,GAAwB,KAAxB;AACA,iBAAO,KAAP;AACD,SALiD,CAOlD;;;AACA,YACE,CAACH,SAAD,IACA,CAACD,UAAU,CAACK,OADZ,IAEA,OAAOJ,SAAS,CAACK,WAAjB,KAAiC,WAHnC,EAIE;AACA,iBAAO,KAAK1C,0BAAZ;AACD;;AAED,YAAI,KAAKwC,gBAAT,EAA2B;AACzB,iBAAO,KAAP;AACD;;AAED,YAAI,CAAC,KAAKzC,UAAV,EAAsB;AACpB,cAAI,KAAKwC,gBAAT,EAA2B;AACzB,mBAAO,IAAP;AACD,WAHmB,CAIpB;AACA;;;AACA,eAAKI,aAAL,GAAqB,KAAKC,mBAAL,EAArB,CANoB,CAOpB;;AACA,cAAI,KAAKD,aAAL,CAAmBE,MAAvB,EAA+B;AAC7B;AACA;AACA,iBAAK,MAAMrC,OAAX,IAAsB,KAAKmC,aAA3B,EAA0C;AACxC;AACA,kBAAI,CAACnC,OAAO,CAACT,UAAT,IAAuBS,OAAO,CAAC+B,gBAAnC,EAAqD;AACnD,qBAAKC,gBAAL,GAAwB,IAAxB;AACA,qBAAKD,gBAAL,GAAwB,KAAxB;AACA,uBAAO,KAAP;AACD;AACF,aAV4B,CAW7B;;;AACA,mBAAO,KAAP;AACD;AACF,SA1CiD,CA4ClD;;;AACA,YAAI,CAAC,KAAKO,2BAAV,EAAuC;AACrC,iBAAO,IAAP;AACD;;AAED,cAAMC,aAAa,GACjB,KAAKC,eAAL,IAAwB,IAAxB,GACI,CADJ,GAEIX,SAAS,CAACY,QAAV,GAAqB,KAAKD,eAHhC,CAjDkD,CAqDlD;;AACA,cAAM;AAAEE,UAAAA,OAAF;AAAWC,UAAAA;AAAX,YAAsB,KAAKC,wBAAL,CAC1B,KAAKC,SAAL,EAD0B,EAE1BjB,UAF0B,EAG1B,EACE,GAAGC,SADL;AAEEU,UAAAA;AAFF,SAH0B,CAA5B;;AASA,YAAII,MAAJ,EAAY;AACV,eAAKG,mBAAL,CAAyBjB,SAAzB;AACA,eAAKG,gBAAL,GAAwB,IAAxB;AACD;;AACD,eAAOU,OAAP;AACD,OApED;;AAsEA,YAAMK,MAAM,GAAG,KAAKC,eAAL,EAAf,CA1EW,CA2EX;;AACAhD,MAAAA,OAAO,CAACiD,GAAR,CAAY,EAAE,GAAGF,MAAL;AAAapB,QAAAA;AAAb,OAAZ;AACD,KArYa;;AACZ,SAAKtC,eAAL,GAAuBJ,gBAAgB,EAAvC;AACA,SAAKqD,2BAAL,GAAmC,KAAnC;AACD;;AAEDO,EAAAA,SAAS,GAAG;AACV,WAAO,KAAK/C,MAAZ;AACD;;AAEDoD,EAAAA,cAAc,CAACC,QAAD,EAAiB,CAAE;;AAEjClD,EAAAA,oBAAoB,CAACd,EAAD,EAAa;AAC/B,WAAO,KAAKiE,eAAL,CAAqBjE,EAArB,CAAP;AACD;;AAEDkE,EAAAA,iBAAiB,CAACrD,OAAD,EAAgB;AAC/B,SAAKoD,eAAL,CAAqBpD,OAAO,CAACb,EAA7B,IAAmCa,OAAnC;AACD;;AAED4C,EAAAA,wBAAwB,CACtBU,OADsB,EAEtBC,WAFsB,EAGtBC,MAHsB,EAImB;AACzC,WAAO;AAAEd,MAAAA,OAAO,EAAE;AAAX,KAAP;AACD;;AAEqB,MAAlBe,kBAAkB,GAAqB;AACzC,UAAM,IAAIhE,KAAJ,CAAU,iDAAV,CAAN;AACD;;AAEDiE,EAAAA,iCAAiC,CAACJ,OAAD,EAAkB;AACjD,WAAO,IAAP;AACD;;AAUDK,EAAAA,mBAAmB,CAAC;AAAE7B,IAAAA,OAAO,GAAG,IAAZ;AAAkB,OAAG8B;AAArB,GAAD,EAA+B;AAChD,SAAK1D,kBAAL;AAEA,SAAKJ,MAAL,GAAc,KAAK+D,YAAL,CAAkB;AAAE/B,MAAAA,OAAF;AAAW,SAAG8B;AAAd,KAAlB,CAAd;AACA,SAAKtB,2BAAL,GAAmC,KAAKoB,iCAAL,CACjC,KAAK5D,MAD4B,CAAnC;;AAGA,QAAIF,KAAK,CAACC,OAAN,CAAc,KAAKC,MAAL,CAAYC,OAA1B,CAAJ,EAAwC;AACtC,WAAK,MAAMC,OAAX,IAAsB,KAAKF,MAAL,CAAYC,OAAlC,EAA2C;AACzCC,QAAAA,OAAO,CAACqD,iBAAR,CAA0B,IAA1B;AACD;AACF;;AAED,QAAI,KAAKlD,MAAT,EAAiB;AACf,WAAK2D,IAAL;AACD;;AACD,WAAO,KAAKhE,MAAZ;AACD;;AAoBDiE,EAAAA,QAAQ,CAACC,IAAD,EAAqC;AAC3C;AACA,QAAIA,IAAI,IAAI,CAAZ,EAAe;AACb,aAAO,CAAP;AACD;;AACD,WAAOlF,QAAQ,CAACkF,IAAD,CAAf;AACD;;AAED1C,EAAAA,kBAAkB,CAACD,KAAD,EAAwB;AACxC,UAAM;AAAE4C,MAAAA,SAAF;AAAa/B,MAAAA,WAAW,EAAEgC;AAA1B,QAA+C7C,KAArD,CADwC,CAExC;;AACA,UAAM8C,YAAY,GAAG9C,KAAK,CAAC+C,eAAN,CAAsB,CAAtB,CAArB;AACA,UAAMzD,aAAa,GAAG,KAAK0D,aAAL,CAAmB;AACvC/D,MAAAA,CAAC,EAAE6D,YAAY,CAACG,OADuB;AAEvC/D,MAAAA,CAAC,EAAE4D,YAAY,CAACI;AAFuB,KAAnB,CAAtB,CAJwC,CASxC;;AACA,UAAM9C,KAAK,GAAG,KAAKsC,QAAL,CAAcE,SAAd,CAAd;;AACA,QAAIxC,KAAK,KAAK,KAAK+C,aAAnB,EAAkC;AAChC,WAAKC,QAAL,GAAgB,KAAKD,aAArB;AACA,WAAKA,aAAL,GAAqB/C,KAArB;AACD;;AAED,WAAO;AACLT,MAAAA,WAAW,EAAE;AACXkD,QAAAA,gBADW;AAEXzC,QAAAA,KAFW;AAGXd,QAAAA,aAHW;AAIX,WAAG,KAAK+D,oBAAL,CAA0BrD,KAA1B,CAJQ;AAKX;AACAsD,QAAAA,UAAU,EAAE,KAAKA,UANN;AAOXC,QAAAA,MAAM,EAAE,KAAKC,GAPF;AAQX;AACA;AACA;AACAJ,QAAAA,QAAQ,EACNhD,KAAK,KAAK,KAAK+C,aAAf,IAAgC/C,KAAK,IAAI,CAAzC,GACI,KAAKgD,QADT,GAEIK;AAdK,OADR;AAiBLC,MAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAjBN,KAAP;AAmBD;;AAEDP,EAAAA,oBAAoB,CAAClB,MAAD,EAAyB;AAC3C,WAAO,EAAP;AACD;;AAeD0B,EAAAA,qBAAqB,CAAC7D,KAAD,EAAwB;AAC3C,SAAK,MAAMrB,OAAX,IAAsBmF,MAAM,CAACC,MAAP,CAAc,KAAKhC,eAAnB,CAAtB,EAA2D;AACzD,UAAIpD,OAAO,IAAIA,OAAO,CAAC+B,gBAAvB,EAAyC;AACvC/B,QAAAA,OAAO,CAACgC,gBAAR,GAA2B,IAA3B;AACAhC,QAAAA,OAAO,CAACqF,WAAR,CAAoBhE,KAApB;AACD;AACF;AACF;;AAEDiE,EAAAA,qBAAqB,GAAG;AACtB,SAAK,MAAMtF,OAAX,IAAsBmF,MAAM,CAACC,MAAP,CAAc,KAAKhC,eAAnB,CAAtB,EAA2D;AACzD,UAAIpD,OAAJ,EAAa;AACXA,QAAAA,OAAO,CAACkD,cAAR,CAAuB,IAAvB;AACD;AACF;AACF,GArM2B,CAuM5B;;;AACAqC,EAAAA,cAAc,CAAClE,KAAD,EAAwB;AACpC,SAAKU,gBAAL,GAAwB,KAAxB;AACA,SAAKmD,qBAAL,CAA2B7D,KAA3B;AACD;;AAEDmE,EAAAA,eAAe,CAACnE,KAAD,EAAwB;AACrC,QAAI,KAAKU,gBAAT,EAA2B;AACzB,WAAKC,gBAAL,GAAwB,IAAxB;AACA,WAAKqD,WAAL,CAAiBhE,KAAjB;AACD;AACF;;AAEDgE,EAAAA,WAAW,CAAChE,KAAD,EAAwB;AACjC,SAAKiE,qBAAL;AACA,SAAKG,SAAL,CAAe,EACb,GAAGpE,KADU;AAEb4C,MAAAA,SAAS,EAAEtF,MAAM,CAAC+G,YAFL;AAGbC,MAAAA,OAAO,EAAE;AAHI,KAAf;AAKA,SAAKJ,cAAL,CAAoBlE,KAApB;AACD;;AAEDuE,EAAAA,UAAU,CAAC;AAAEC,IAAAA;AAAF,GAAD,EAA8B;AACtC,QAAIA,OAAJ,EAAa;AACX,WAAK7D,gBAAL,GAAwB,KAAxB;AACD;AACF;;AAED8D,EAAAA,oBAAoB,CAAChG,MAAD,EAAiB;AAAA;;AACnC,8DACEA,MAAM,CAACiG,oBADT,2DACE,uBAA6BC,IAA7B,CAAmCC,OAAD,IAAaA,OAAO,CAAC3G,QAAvD,CADF,yEACsE,KADtE;AAGD;;AAED4G,EAAAA,OAAO,CAACrB,GAAD,EAA8C1D,QAA9C,EAA6D;AAClE,QAAI0D,GAAG,IAAI,IAAX,EAAiB;AACf,WAAKxE,OAAL;AACA,WAAKI,IAAL,GAAY,IAAZ;AACA;AACD,KALiE,CAOlE;;;AACA,UAAM0F,cAAc,IAAG,kBAAkBC,MAArB,CAApB;AACA,SAAKjF,QAAL,GAAgBA,QAAhB;AACA,SAAK0D,GAAL,GAAWA,GAAX;AAEA,SAAKpE,IAAL,GAAY7B,cAAc,CAACiG,GAAD,CAA1B,CAZkE,CAclE;AACA;AACA;;AACA,SAAK1E,MAAL,GACEgG,cAAc,IAAI,KAAKL,oBAAL,CAA0B,KAAKhG,MAA/B,CAAlB,GACI,IAAInB,MAAM,CAAC0H,OAAX,CAAmB,KAAK5F,IAAxB,EAAqC;AACnC6F,MAAAA,UAAU,EAAE3H,MAAM,CAAC4H;AADgB,KAArC,CADJ,GAII,IAAI5H,MAAM,CAAC0H,OAAX,CAAmB,KAAK5F,IAAxB,CALN;AAOA,SAAKgE,QAAL,GAAgB5F,KAAK,CAACc,YAAtB;AACA,SAAK6E,aAAL,GAAqB3F,KAAK,CAACc,YAA3B;AACA,SAAK6B,aAAL,GAAqB,IAArB;AAEA,UAAM;AAAEiC,MAAAA;AAAF,QAAyB,IAA/B,CA5BkE,CA6BlE;;AACA,UAAMzD,OAAO,GAAG,IAAIyD,kBAAJ,CAAuB,KAAKT,eAAL,EAAvB,CAAhB;AACA,SAAK7C,MAAL,CAAYqG,GAAZ,CAAgBxG,OAAhB;AAEA,SAAKG,MAAL,CAAYsG,EAAZ,CAAe,cAAf,EAAgCC,EAAD,IAAqB;AAClD,UAAI,CAAC,KAAK5G,MAAL,CAAYgC,OAAjB,EAA0B;AACxB,aAAKE,gBAAL,GAAwB,KAAxB;AACA,aAAKD,gBAAL,GAAwB,KAAxB;AACA;AACD;;AAED,WAAK6D,UAAL,CAAgBc,EAAhB,EAPkD,CASlD;AACA;;AACA,UAAI,KAAKlE,eAAL,KAAyB,IAAzB,IAAiCkE,EAAE,CAACjE,QAAH,KAAgB,CAArD,EAAwD;AACtD,aAAKD,eAAL,GAAuBkE,EAAE,CAACjE,QAA1B;AACD;;AACD,UAAIiE,EAAE,CAACf,OAAP,EAAgB;AACd;AACAgB,QAAAA,UAAU,CAAC,MAAM;AACf,eAAKnE,eAAL,GAAuB,IAAvB;AACA,eAAKR,gBAAL,GAAwB,KAAxB;AACD,SAHS,CAAV;AAID;AACF,KArBD;AAuBA,SAAK4E,WAAL;AACA,SAAK9C,IAAL;AACD;;AAED8C,EAAAA,WAAW,GAAG;AACZ;AACA,QAAI,CAAC,KAAKrH,UAAV,EAAsB;AACpB,WAAKY,MAAL,CAAasG,EAAb,CAAiB,GAAE,KAAKrH,IAAK,OAA7B,EAAsCiC,KAAD,IACnC,KAAKwF,OAAL,CAAaxF,KAAb,CADF;AAGA,WAAKlB,MAAL,CAAasG,EAAb,CACG,GAAE,KAAKrH,IAAK,OAAM,KAAKA,IAAK,QAD/B,EAEGiC,KAAD,IAAwB;AACtB,aAAKkE,cAAL,CAAoBlE,KAApB;AACD,OAJH;AAMD;;AACD,SAAKlB,MAAL,CAAasG,EAAb,CAAgB,KAAKrH,IAArB,EAA4BsH,EAAD,IACzB,KAAKI,kBAAL,CAAwBJ,EAAxB,CADF,EAbY,CAeT;AACJ;;AAEDG,EAAAA,OAAO,CAAC;AAAEE,IAAAA,MAAF;AAAUC,IAAAA,MAAV;AAAkBvE,IAAAA;AAAlB,GAAD,EAA+C;AACpD;AACA,SAAKgC,QAAL,GAAgB5F,KAAK,CAACc,YAAtB;AACA,SAAK6E,aAAL,GAAqB3F,KAAK,CAACc,YAA3B;AACA,SAAK6B,aAAL,GAAqB,IAArB;AAEA,SAAKO,gBAAL,GAAwB,IAAxB;AACA,SAAKkF,UAAL,GAAkBF,MAAlB;AACA,SAAKG,UAAL,GAAkBF,MAAlB;AACA,SAAKxE,eAAL,GAAuBC,QAAvB;AACD;;AAEDqE,EAAAA,kBAAkB,CAACJ,EAAD,EAAqB;AACrC,SAAKjB,SAAL,CAAeiB,EAAf;AACD;;AAEDS,EAAAA,SAAS,GAAG,CAAE;;AAEd/E,EAAAA,mBAAmB,GAAG;AACpB,QAAIxC,KAAK,CAACC,OAAN,CAAc,KAAKC,MAAL,CAAYC,OAA1B,KAAsC,KAAKD,MAAL,CAAYC,OAAZ,CAAoBsC,MAA9D,EAAsE;AACpE;AACA;AACA,YAAM+E,YAAY,GAAG,KAAKtH,MAAL,CAAYC,OAAZ,CAAoBsH,MAApB,CACnB,CAAC;AAAErF,QAAAA;AAAF,OAAD,KAA0BA,gBAAgB,KAAK,KAD5B,CAArB;AAGA,aAAOoF,YAAP;AACD;;AACD,WAAO,EAAP;AACD;;AAEDpE,EAAAA,eAAe,GAAG;AAChB,UAAMsE,QAAQ,GACZ,KAAKxH,MAAL,CAAYyH,WAAZ,KAA4B,KAAKzH,MAAL,CAAYoC,WAAxC,GACI,KAAKpC,MAAL,CAAYyH,WADhB,GAEI,CAHN;AAIA,WAAO;AACLD,MAAAA;AADK,KAAP;AAGD;;AAiFDxE,EAAAA,mBAAmB,CAAC0E,UAAD,EAAkB,CAAE,CA/aX,CAib5B;;;AACA3D,EAAAA,YAAY,CAAC/D,MAAD,EAAmC;AAC7C,UAAM8D,KAAK,GAAG,EAAE,GAAG9D;AAAL,KAAd,CAD6C,CAG7C;;AACA,QAAI,aAAaA,MAAjB,EAAyB;AACvB8D,MAAAA,KAAK,CAAC6D,OAAN,GAAgB3H,MAAM,CAAC2H,OAAvB;AACA7D,MAAAA,KAAK,CAAC8D,SAAN,GAAkB9D,KAAK,CAAC6D,OAAN,GAAiB7D,KAAK,CAAC6D,OAAzC;AACD;;AACD,QAAI,iBAAiB3H,MAArB,EAA6B;AAC3B8D,MAAAA,KAAK,CAAC+D,WAAN,GAAoB7H,MAAM,CAAC6H,WAA3B;AACA/D,MAAAA,KAAK,CAACgE,aAAN,GAAsBhE,KAAK,CAAC+D,WAAN,GAAqB/D,KAAK,CAAC+D,WAAjD;AACD;;AACD,QAAI,aAAa7H,MAAjB,EAAyB;AACvB8D,MAAAA,KAAK,CAACiE,OAAN,GAAgB/H,MAAM,CAAC+H,OAAvB;AACAjE,MAAAA,KAAK,CAACkE,SAAN,GAAkBhI,MAAM,CAAC+H,OAAP,GAAkB/H,MAAM,CAAC+H,OAA3C;AACD;;AACD,QAAI,aAAa/H,MAAjB,EAAyB;AACvB8D,MAAAA,KAAK,CAAC7D,OAAN,GAAgBgI,OAAO,CAACjI,MAAM,CAACC,OAAR,CAAP,CACbiI,GADa,CACT,CAAC;AAAErD,QAAAA;AAAF,OAAD,KACH5F,WAAW,CAACkJ,UAAZ,CAAuBtD,UAAvB,CAFY,EAIb0C,MAJa,CAILa,CAAD,IAAOA,CAJD,CAAhB;AAKD,KAND,MAMO;AACLtE,MAAAA,KAAK,CAAC7D,OAAN,GAAgB,IAAhB;AACD;;AACD,QAAI,0BAA0BD,MAA9B,EAAsC;AACpC,YAAMgG,oBAAoB,GAAG,KAAKA,oBAAL,CAA0B,KAAKhG,MAA/B,CAA7B;AACA8D,MAAAA,KAAK,CAACmC,oBAAN,GAA6BgC,OAAO,CAACjI,MAAM,CAACiG,oBAAR,CAAP,CAC1BiC,GAD0B,CACrB/B,OAAD,IAAsC;AACzC,YAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,iBAAOlH,WAAW,CAACkJ,UAAZ,CAAuBhC,OAAvB,CAAP;AACD,SAFD,MAEO;AACL,iBAAOlH,WAAW,CAACkJ,UAAZ,CAAuBhC,OAAO,CAACtB,UAA/B,CAAP;AACD;AACF,OAP0B,EAQ1B0C,MAR0B,CAQlBa,CAAD,IAAOA,CARY,CAA7B;;AAUA,UAAIpC,oBAAoB,KAAK,KAAKA,oBAAL,CAA0BlC,KAA1B,CAA7B,EAA+D;AAC7D5E,QAAAA,gBAAgB,CAAC,MAAM;AACrB;AACA;AACA,eAAKqB,OAAL;AACA,eAAK6F,OAAL,CAAa,KAAKrB,GAAlB,EAAuB,KAAK1D,QAA5B;AACD,SALe,CAAhB;AAMD;AACF,KApBD,MAoBO;AACLyC,MAAAA,KAAK,CAACmC,oBAAN,GAA6B,IAA7B;AACD;;AAED,UAAMoC,WAAW,GAAG,CAClB,aADkB,EAElB,aAFkB,EAGlB,SAHkB,EAIlB,SAJkB,EAKlB,WALkB,EAMlB,eANkB,EAOlB,WAPkB,EAQlB,aARkB,EASlB,kBATkB,EAUlB,kBAVkB,EAWlB,gBAXkB,EAYlB,gBAZkB,EAalB,oBAbkB,EAclB,kBAdkB,EAelB,oBAfkB,EAgBlB,kBAhBkB,CAApB;AAkBAA,IAAAA,WAAW,CAACC,OAAZ,CAAqBC,IAAD,IAAwC;AAC1D,UAAI,OAAOzE,KAAK,CAACyE,IAAD,CAAZ,KAAuB,WAA3B,EAAwC;AACtCzE,QAAAA,KAAK,CAACyE,IAAD,CAAL,GAAcC,MAAM,CAACC,GAArB;AACD;AACF,KAJD;AAKA,WAAO3E,KAAP,CAxE6C,CAwEX;AACnC;;AA3f2B,C,CA8f9B;AACA;;;AACA,SAASrC,oBAAT,CACEiH,MADF,EAKEnH,KALF,EAME;AACA,MAAImH,MAAJ,EAAY;AACV,QAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAChCA,MAAAA,MAAM,CAACnH,KAAD,CAAN;AACD,KAFD,MAEO;AACL;AACA,UACE,kBAAkBmH,MAAlB,IACA,OAAOA,MAAM,CAACC,YAAd,KAA+B,UAFjC,EAGE;AACA,cAAMxC,OAAO,GAAGuC,MAAM,CAACC,YAAP,EAAhB;;AACAlH,QAAAA,oBAAoB,CAAC0E,OAAD,EAAU5E,KAAV,CAApB;AACD,OAND,MAMO;AACL,YAAI,kBAAkBmH,MAAtB,EAA8B;AAC5B,gBAAM;AAAEE,YAAAA;AAAF,cAAiBF,MAAM,CAACG,YAA9B;;AACA,cAAI/I,KAAK,CAACC,OAAN,CAAc6I,UAAd,CAAJ,EAA+B;AAC7B,iBAAK,MAAM,CAACE,KAAD,EAAQ,CAACC,GAAD,EAAMC,KAAN,CAAR,CAAX,IAAoCJ,UAAU,CAACK,OAAX,EAApC,EAA0D;AACxD,kBAAIF,GAAG,IAAIxH,KAAK,CAACL,WAAjB,EAA8B;AAC5B;AACA,sBAAMgI,WAAW,GAAG3H,KAAK,CAACL,WAAN,CAAkB6H,GAAlB,CAApB;;AACA,oBAAIC,KAAK,IAAIA,KAAK,CAACG,QAAnB,EAA6B;AAC3B;AACAH,kBAAAA,KAAK,CAACG,QAAN,CAAeD,WAAf;AACD,iBAHD,MAGO;AACL;AACAR,kBAAAA,MAAM,CAACG,YAAP,CAAoBD,UAApB,CAA+BE,KAA/B,IAAwC,CAACC,GAAD,EAAMG,WAAN,CAAxC;AACD;AACF;AACF;AACF;AACF;AACF;AACF;AACF;AACF;;AAED,SAASjB,OAAT,CAAoBe,KAApB,EAAoC;AAClC;AACA,SAAOA,KAAK,IAAI,IAAT,GAAgB,EAAhB,GAAqBlJ,KAAK,CAACC,OAAN,CAAciJ,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,CAA3D;AACD;;AAED,eAAe5J,cAAf", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport Hammer from '@egjs/hammerjs';\nimport { findNodeHandle } from 'react-native';\n\nimport { State } from '../State';\nimport { EventMap } from './constants';\nimport * as NodeManager from './NodeManager';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\n\n// TODO(TS) Replace with HammerInput if https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50438/files is merged\nexport type HammerInputExt = Omit<HammerInput, 'destroy' | 'handler' | 'init'>;\n\nexport type Config = Partial<{\n  enabled: boolean;\n  minPointers: number;\n  maxPointers: number;\n  minDist: number;\n  minDistSq: number;\n  minVelocity: number;\n  minVelocitySq: number;\n  maxDist: number;\n  maxDistSq: number;\n  failOffsetXStart: number;\n  failOffsetYStart: number;\n  failOffsetXEnd: number;\n  failOffsetYEnd: number;\n  activeOffsetXStart: number;\n  activeOffsetXEnd: number;\n  activeOffsetYStart: number;\n  activeOffsetYEnd: number;\n  waitFor: any[] | null;\n  simultaneousHandlers: any[] | null;\n}>;\n\ntype NativeEvent = ReturnType<GestureHandler['transformEventData']>;\n\nlet gestureInstances = 0;\n\nabstract class GestureHandler {\n  public handlerTag: any;\n  public isGestureRunning = false;\n  public view: number | null = null;\n  protected hasCustomActivationCriteria: boolean;\n  protected hasGestureFailed = false;\n  protected hammer: HammerManager | null = null;\n  protected initialRotation: number | null = null;\n  protected __initialX: any;\n  protected __initialY: any;\n  protected config: Config = {};\n  protected previousState: State = State.UNDETERMINED;\n  private pendingGestures: Record<string, this> = {};\n  private oldState: State = State.UNDETERMINED;\n  private lastSentState: State | null = null;\n  private gestureInstance: number;\n  private _stillWaiting: any;\n  private propsRef: any;\n  private ref: any;\n\n  abstract get name(): string;\n\n  get id() {\n    return `${this.name}${this.gestureInstance}`;\n  }\n\n  // a simple way to check if GestureHandler is NativeViewGestureHandler, since importing it\n  // here to use instanceof would cause import cycle\n  get isNative() {\n    return false;\n  }\n\n  get isDiscrete() {\n    return false;\n  }\n\n  get shouldEnableGestureOnSetup(): boolean {\n    throw new Error('Must override GestureHandler.shouldEnableGestureOnSetup');\n  }\n\n  constructor() {\n    this.gestureInstance = gestureInstances++;\n    this.hasCustomActivationCriteria = false;\n  }\n\n  getConfig() {\n    return this.config;\n  }\n\n  onWaitingEnded(_gesture: this) {}\n\n  removePendingGesture(id: string) {\n    delete this.pendingGestures[id];\n  }\n\n  addPendingGesture(gesture: this) {\n    this.pendingGestures[gesture.id] = gesture;\n  }\n\n  isGestureEnabledForEvent(\n    _config: any,\n    _recognizer: any,\n    _event: any\n  ): { failed?: boolean; success?: boolean } {\n    return { success: true };\n  }\n\n  get NativeGestureClass(): RecognizerStatic {\n    throw new Error('Must override GestureHandler.NativeGestureClass');\n  }\n\n  updateHasCustomActivationCriteria(_config: Config) {\n    return true;\n  }\n\n  clearSelfAsPending = () => {\n    if (Array.isArray(this.config.waitFor)) {\n      for (const gesture of this.config.waitFor) {\n        gesture.removePendingGesture(this.id);\n      }\n    }\n  };\n\n  updateGestureConfig({ enabled = true, ...props }) {\n    this.clearSelfAsPending();\n\n    this.config = this.ensureConfig({ enabled, ...props });\n    this.hasCustomActivationCriteria = this.updateHasCustomActivationCriteria(\n      this.config\n    );\n    if (Array.isArray(this.config.waitFor)) {\n      for (const gesture of this.config.waitFor) {\n        gesture.addPendingGesture(this);\n      }\n    }\n\n    if (this.hammer) {\n      this.sync();\n    }\n    return this.config;\n  }\n\n  destroy = () => {\n    this.clearSelfAsPending();\n\n    if (this.hammer) {\n      this.hammer.stop(false);\n      this.hammer.destroy();\n    }\n    this.hammer = null;\n  };\n\n  isPointInView = ({ x, y }: { x: number; y: number }) => {\n    // @ts-ignore FIXME(TS)\n    const rect = this.view!.getBoundingClientRect();\n    const pointerInside =\n      x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;\n    return pointerInside;\n  };\n\n  getState(type: keyof typeof EventMap): State {\n    // @ts-ignore TODO(TS) check if this is needed\n    if (type == 0) {\n      return 0;\n    }\n    return EventMap[type];\n  }\n\n  transformEventData(event: HammerInputExt) {\n    const { eventType, maxPointers: numberOfPointers } = event;\n    // const direction = DirectionMap[ev.direction];\n    const changedTouch = event.changedPointers[0];\n    const pointerInside = this.isPointInView({\n      x: changedTouch.clientX,\n      y: changedTouch.clientY,\n    });\n\n    // TODO(TS) Remove cast after https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50966 is merged.\n    const state = this.getState(eventType as 1 | 2 | 4 | 8);\n    if (state !== this.previousState) {\n      this.oldState = this.previousState;\n      this.previousState = state;\n    }\n\n    return {\n      nativeEvent: {\n        numberOfPointers,\n        state,\n        pointerInside,\n        ...this.transformNativeEvent(event),\n        // onHandlerStateChange only\n        handlerTag: this.handlerTag,\n        target: this.ref,\n        // send oldState only when the state was changed, or is different than ACTIVE\n        // GestureDetector relies on the presence of `oldState` to differentiate between\n        // update events and state change events\n        oldState:\n          state !== this.previousState || state != 4\n            ? this.oldState\n            : undefined,\n      },\n      timeStamp: Date.now(),\n    };\n  }\n\n  transformNativeEvent(_event: HammerInputExt) {\n    return {};\n  }\n\n  sendEvent = (nativeEvent: HammerInputExt) => {\n    const { onGestureHandlerEvent, onGestureHandlerStateChange } =\n      this.propsRef.current;\n\n    const event = this.transformEventData(nativeEvent);\n\n    invokeNullableMethod(onGestureHandlerEvent, event);\n    if (this.lastSentState !== event.nativeEvent.state) {\n      this.lastSentState = event.nativeEvent.state as State;\n      invokeNullableMethod(onGestureHandlerStateChange, event);\n    }\n  };\n\n  cancelPendingGestures(event: HammerInputExt) {\n    for (const gesture of Object.values(this.pendingGestures)) {\n      if (gesture && gesture.isGestureRunning) {\n        gesture.hasGestureFailed = true;\n        gesture.cancelEvent(event);\n      }\n    }\n  }\n\n  notifyPendingGestures() {\n    for (const gesture of Object.values(this.pendingGestures)) {\n      if (gesture) {\n        gesture.onWaitingEnded(this);\n      }\n    }\n  }\n\n  // FIXME event is undefined in runtime when firstly invoked (see Draggable example), check other functions taking event as input\n  onGestureEnded(event: HammerInputExt) {\n    this.isGestureRunning = false;\n    this.cancelPendingGestures(event);\n  }\n\n  forceInvalidate(event: HammerInputExt) {\n    if (this.isGestureRunning) {\n      this.hasGestureFailed = true;\n      this.cancelEvent(event);\n    }\n  }\n\n  cancelEvent(event: HammerInputExt) {\n    this.notifyPendingGestures();\n    this.sendEvent({\n      ...event,\n      eventType: Hammer.INPUT_CANCEL,\n      isFinal: true,\n    });\n    this.onGestureEnded(event);\n  }\n\n  onRawEvent({ isFirst }: HammerInputExt) {\n    if (isFirst) {\n      this.hasGestureFailed = false;\n    }\n  }\n\n  shouldUseTouchEvents(config: Config) {\n    return (\n      config.simultaneousHandlers?.some((handler) => handler.isNative) ?? false\n    );\n  }\n\n  setView(ref: Parameters<typeof findNodeHandle>['0'], propsRef: any) {\n    if (ref == null) {\n      this.destroy();\n      this.view = null;\n      return;\n    }\n\n    // @ts-ignore window doesn't exist on global type as we don't want to use Node types\n    const SUPPORTS_TOUCH = 'ontouchstart' in window;\n    this.propsRef = propsRef;\n    this.ref = ref;\n\n    this.view = findNodeHandle(ref);\n\n    // When the browser starts handling the gesture (e.g. scrolling), it sends a pointercancel event and stops\n    // sending additional pointer events. This is not the case with touch events, so if the gesture is simultaneous\n    // with a NativeGestureHandler, we need to check if touch events are supported and use them if possible.\n    this.hammer =\n      SUPPORTS_TOUCH && this.shouldUseTouchEvents(this.config)\n        ? new Hammer.Manager(this.view as any, {\n            inputClass: Hammer.TouchInput,\n          })\n        : new Hammer.Manager(this.view as any);\n\n    this.oldState = State.UNDETERMINED;\n    this.previousState = State.UNDETERMINED;\n    this.lastSentState = null;\n\n    const { NativeGestureClass } = this;\n    // @ts-ignore TODO(TS)\n    const gesture = new NativeGestureClass(this.getHammerConfig());\n    this.hammer.add(gesture);\n\n    this.hammer.on('hammer.input', (ev: HammerInput) => {\n      if (!this.config.enabled) {\n        this.hasGestureFailed = false;\n        this.isGestureRunning = false;\n        return;\n      }\n\n      this.onRawEvent(ev as unknown as HammerInputExt);\n\n      // TODO: Bacon: Check against something other than null\n      // The isFirst value is not called when the first rotation is calculated.\n      if (this.initialRotation === null && ev.rotation !== 0) {\n        this.initialRotation = ev.rotation;\n      }\n      if (ev.isFinal) {\n        // in favor of a willFail otherwise the last frame of the gesture will be captured.\n        setTimeout(() => {\n          this.initialRotation = null;\n          this.hasGestureFailed = false;\n        });\n      }\n    });\n\n    this.setupEvents();\n    this.sync();\n  }\n\n  setupEvents() {\n    // TODO(TS) Hammer types aren't exactly that what we get in runtime\n    if (!this.isDiscrete) {\n      this.hammer!.on(`${this.name}start`, (event: HammerInput) =>\n        this.onStart(event as unknown as HammerInputExt)\n      );\n      this.hammer!.on(\n        `${this.name}end ${this.name}cancel`,\n        (event: HammerInput) => {\n          this.onGestureEnded(event as unknown as HammerInputExt);\n        }\n      );\n    }\n    this.hammer!.on(this.name, (ev: HammerInput) =>\n      this.onGestureActivated(ev as unknown as HammerInputExt)\n    ); // TODO(TS) remove cast after https://github.com/DefinitelyTyped/DefinitelyTyped/pull/50438 is merged\n  }\n\n  onStart({ deltaX, deltaY, rotation }: HammerInputExt) {\n    // Reset the state for the next gesture\n    this.oldState = State.UNDETERMINED;\n    this.previousState = State.UNDETERMINED;\n    this.lastSentState = null;\n\n    this.isGestureRunning = true;\n    this.__initialX = deltaX;\n    this.__initialY = deltaY;\n    this.initialRotation = rotation;\n  }\n\n  onGestureActivated(ev: HammerInputExt) {\n    this.sendEvent(ev);\n  }\n\n  onSuccess() {}\n\n  _getPendingGestures() {\n    if (Array.isArray(this.config.waitFor) && this.config.waitFor.length) {\n      // Get the list of gestures that this gesture is still waiting for.\n      // Use `=== false` in case a ref that isn't a gesture handler is used.\n      const stillWaiting = this.config.waitFor.filter(\n        ({ hasGestureFailed }) => hasGestureFailed === false\n      );\n      return stillWaiting;\n    }\n    return [];\n  }\n\n  getHammerConfig() {\n    const pointers =\n      this.config.minPointers === this.config.maxPointers\n        ? this.config.minPointers\n        : 0;\n    return {\n      pointers,\n    };\n  }\n\n  sync = () => {\n    const gesture = this.hammer!.get(this.name);\n    if (!gesture) return;\n\n    const enable = (recognizer: any, inputData: any) => {\n      if (!this.config.enabled) {\n        this.isGestureRunning = false;\n        this.hasGestureFailed = false;\n        return false;\n      }\n\n      // Prevent events before the system is ready.\n      if (\n        !inputData ||\n        !recognizer.options ||\n        typeof inputData.maxPointers === 'undefined'\n      ) {\n        return this.shouldEnableGestureOnSetup;\n      }\n\n      if (this.hasGestureFailed) {\n        return false;\n      }\n\n      if (!this.isDiscrete) {\n        if (this.isGestureRunning) {\n          return true;\n        }\n        // The built-in hammer.js \"waitFor\" doesn't work across multiple views.\n        // Only process if there are views to wait for.\n        this._stillWaiting = this._getPendingGestures();\n        // This gesture should continue waiting.\n        if (this._stillWaiting.length) {\n          // Check to see if one of the gestures you're waiting for has started.\n          // If it has then the gesture should fail.\n          for (const gesture of this._stillWaiting) {\n            // When the target gesture has started, this gesture must force fail.\n            if (!gesture.isDiscrete && gesture.isGestureRunning) {\n              this.hasGestureFailed = true;\n              this.isGestureRunning = false;\n              return false;\n            }\n          }\n          // This gesture shouldn't start until the others have finished.\n          return false;\n        }\n      }\n\n      // Use default behaviour\n      if (!this.hasCustomActivationCriteria) {\n        return true;\n      }\n\n      const deltaRotation =\n        this.initialRotation == null\n          ? 0\n          : inputData.rotation - this.initialRotation;\n      // @ts-ignore FIXME(TS)\n      const { success, failed } = this.isGestureEnabledForEvent(\n        this.getConfig(),\n        recognizer,\n        {\n          ...inputData,\n          deltaRotation,\n        }\n      );\n\n      if (failed) {\n        this.simulateCancelEvent(inputData);\n        this.hasGestureFailed = true;\n      }\n      return success;\n    };\n\n    const params = this.getHammerConfig();\n    // @ts-ignore FIXME(TS)\n    gesture.set({ ...params, enable });\n  };\n\n  simulateCancelEvent(_inputData: any) {}\n\n  // Validate the props\n  ensureConfig(config: Config): Required<Config> {\n    const props = { ...config };\n\n    // TODO(TS) We use ! to assert that if property is present then value is not empty (null, undefined)\n    if ('minDist' in config) {\n      props.minDist = config.minDist;\n      props.minDistSq = props.minDist! * props.minDist!;\n    }\n    if ('minVelocity' in config) {\n      props.minVelocity = config.minVelocity;\n      props.minVelocitySq = props.minVelocity! * props.minVelocity!;\n    }\n    if ('maxDist' in config) {\n      props.maxDist = config.maxDist;\n      props.maxDistSq = config.maxDist! * config.maxDist!;\n    }\n    if ('waitFor' in config) {\n      props.waitFor = asArray(config.waitFor)\n        .map(({ handlerTag }: { handlerTag: number }) =>\n          NodeManager.getHandler(handlerTag)\n        )\n        .filter((v) => v);\n    } else {\n      props.waitFor = null;\n    }\n    if ('simultaneousHandlers' in config) {\n      const shouldUseTouchEvents = this.shouldUseTouchEvents(this.config);\n      props.simultaneousHandlers = asArray(config.simultaneousHandlers)\n        .map((handler: number | GestureHandler) => {\n          if (typeof handler === 'number') {\n            return NodeManager.getHandler(handler);\n          } else {\n            return NodeManager.getHandler(handler.handlerTag);\n          }\n        })\n        .filter((v) => v);\n\n      if (shouldUseTouchEvents !== this.shouldUseTouchEvents(props)) {\n        ghQueueMicrotask(() => {\n          // if the undelying event API needs to be changed, we need to unmount and mount\n          // the hammer instance again.\n          this.destroy();\n          this.setView(this.ref, this.propsRef);\n        });\n      }\n    } else {\n      props.simultaneousHandlers = null;\n    }\n\n    const configProps = [\n      'minPointers',\n      'maxPointers',\n      'minDist',\n      'maxDist',\n      'maxDistSq',\n      'minVelocitySq',\n      'minDistSq',\n      'minVelocity',\n      'failOffsetXStart',\n      'failOffsetYStart',\n      'failOffsetXEnd',\n      'failOffsetYEnd',\n      'activeOffsetXStart',\n      'activeOffsetXEnd',\n      'activeOffsetYStart',\n      'activeOffsetYEnd',\n    ] as const;\n    configProps.forEach((prop: (typeof configProps)[number]) => {\n      if (typeof props[prop] === 'undefined') {\n        props[prop] = Number.NaN;\n      }\n    });\n    return props as Required<Config>; // TODO(TS) how to convince TS that props are filled?\n  }\n}\n\n// TODO(TS) investigate this method\n// Used for sending data to a callback or AnimatedEvent\nfunction invokeNullableMethod(\n  method:\n    | ((event: NativeEvent) => void)\n    | { __getHandler: () => (event: NativeEvent) => void }\n    | { __nodeConfig: { argMapping: any } },\n  event: NativeEvent\n) {\n  if (method) {\n    if (typeof method === 'function') {\n      method(event);\n    } else {\n      // For use with reanimated's AnimatedEvent\n      if (\n        '__getHandler' in method &&\n        typeof method.__getHandler === 'function'\n      ) {\n        const handler = method.__getHandler();\n        invokeNullableMethod(handler, event);\n      } else {\n        if ('__nodeConfig' in method) {\n          const { argMapping } = method.__nodeConfig;\n          if (Array.isArray(argMapping)) {\n            for (const [index, [key, value]] of argMapping.entries()) {\n              if (key in event.nativeEvent) {\n                // @ts-ignore fix method type\n                const nativeValue = event.nativeEvent[key];\n                if (value && value.setValue) {\n                  // Reanimated API\n                  value.setValue(nativeValue);\n                } else {\n                  // RN Animated API\n                  method.__nodeConfig.argMapping[index] = [key, nativeValue];\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\nfunction asArray<T>(value: T | T[]) {\n  // TODO(TS) use config.waitFor type\n  return value == null ? [] : Array.isArray(value) ? value : [value];\n}\n\nexport default GestureHandler;\n"]}