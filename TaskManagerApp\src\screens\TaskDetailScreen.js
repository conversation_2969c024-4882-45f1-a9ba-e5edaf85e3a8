import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTask } from '../contexts/TaskContext';
import { useTheme } from '../contexts/ThemeContext';

const TaskDetailScreen = ({ navigation, route }) => {
  const { task } = route.params;
  const { toggleTaskStatus, removeTask } = useTask();
  const { theme } = useTheme();

  useEffect(() => {
    navigation.setOptions({
      title: 'Task Details',
      headerStyle: {
        backgroundColor: theme.colors.background,
      },
      headerTintColor: theme.colors.text,
      headerTitleStyle: {
        color: theme.colors.text,
      },
      headerRight: () => (
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('AddEditTask', { task })}
        >
          <Text style={[styles.headerButtonText, { color: theme.colors.primary }]}>
            Edit
          </Text>
        </TouchableOpacity>
      ),
    });
  }, [navigation, theme, task]);

  const handleToggleStatus = async () => {
    const { error } = await toggleTaskStatus(task.id, task.status);
    if (error) {
      Alert.alert('Error', error.message);
    } else {
      navigation.goBack();
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const { error } = await removeTask(task.id);
            if (error) {
              Alert.alert('Error', error.message);
            } else {
              navigation.goBack();
            }
          },
        },
      ]
    );
  };

  const getPriorityColor = () => {
    switch (task.priority) {
      case 'high':
        return theme.colors.priorityHigh;
      case 'medium':
        return theme.colors.priorityMedium;
      case 'low':
        return theme.colors.priorityLow;
      default:
        return theme.colors.textSecondary;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getDueDateStatus = () => {
    if (!task.due_date) return null;
    
    const dueDate = new Date(task.due_date);
    const now = new Date();
    const diffTime = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (task.status === 'completed') {
      return { text: 'Completed', color: theme.colors.taskCompleted };
    } else if (diffDays < 0) {
      return { text: `Overdue by ${Math.abs(diffDays)} day(s)`, color: theme.colors.error };
    } else if (diffDays === 0) {
      return { text: 'Due today', color: theme.colors.warning };
    } else if (diffDays === 1) {
      return { text: 'Due tomorrow', color: theme.colors.primary };
    } else {
      return { text: `Due in ${diffDays} day(s)`, color: theme.colors.textSecondary };
    }
  };

  const dueDateStatus = getDueDateStatus();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    headerButton: {
      marginRight: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
    },
    headerButtonText: {
      fontSize: theme.fontSize.md,
      fontWeight: '600',
    },
    content: {
      padding: theme.spacing.lg,
    },
    titleSection: {
      marginBottom: theme.spacing.xl,
    },
    title: {
      fontSize: theme.fontSize.xxl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      textDecorationLine: task.status === 'completed' ? 'line-through' : 'none',
      opacity: task.status === 'completed' ? 0.7 : 1,
    },
    statusBadge: {
      alignSelf: 'flex-start',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    statusText: {
      fontSize: theme.fontSize.sm,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    infoSection: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.lg,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    infoRowLast: {
      borderBottomWidth: 0,
    },
    infoLabel: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      fontWeight: '500',
    },
    infoValue: {
      fontSize: theme.fontSize.md,
      color: theme.colors.text,
      fontWeight: '600',
    },
    priorityValue: {
      textTransform: 'capitalize',
    },
    descriptionSection: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.xl,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    descriptionTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    description: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      lineHeight: 22,
    },
    noDescription: {
      fontStyle: 'italic',
      color: theme.colors.textSecondary,
    },
    actionsSection: {
      gap: theme.spacing.md,
    },
    actionButton: {
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    successButton: {
      backgroundColor: theme.colors.taskCompleted,
    },
    dangerButton: {
      backgroundColor: theme.colors.error,
    },
    actionButtonText: {
      fontSize: theme.fontSize.md,
      fontWeight: '600',
      color: theme.colors.fabText,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.titleSection}>
          <Text style={styles.title}>{task.title}</Text>
          
          <View
            style={[
              styles.statusBadge,
              {
                backgroundColor: task.status === 'completed' 
                  ? theme.colors.taskCompleted + '20' 
                  : theme.colors.taskPending + '20',
              },
            ]}
          >
            <Text
              style={[
                styles.statusText,
                {
                  color: task.status === 'completed' 
                    ? theme.colors.taskCompleted 
                    : theme.colors.taskPending,
                },
              ]}
            >
              {task.status}
            </Text>
          </View>
        </View>

        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Priority</Text>
            <Text
              style={[
                styles.infoValue,
                styles.priorityValue,
                { color: getPriorityColor() },
              ]}
            >
              {task.priority}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Due Date</Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>
              {formatDate(task.due_date)}
            </Text>
          </View>
          
          {dueDateStatus && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Status</Text>
              <Text style={[styles.infoValue, { color: dueDateStatus.color }]}>
                {dueDateStatus.text}
              </Text>
            </View>
          )}
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>
              {formatDate(task.created_at)}
            </Text>
          </View>
          
          <View style={[styles.infoRow, styles.infoRowLast]}>
            <Text style={styles.infoLabel}>Last Updated</Text>
            <Text style={styles.infoValue}>
              {formatDate(task.updated_at)}
            </Text>
          </View>
        </View>

        <View style={styles.descriptionSection}>
          <Text style={styles.descriptionTitle}>Description</Text>
          <Text style={[styles.description, !task.description && styles.noDescription]}>
            {task.description || 'No description provided'}
          </Text>
        </View>

        <View style={styles.actionsSection}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              task.status === 'completed' ? styles.primaryButton : styles.successButton,
            ]}
            onPress={handleToggleStatus}
          >
            <Text style={styles.actionButtonText}>
              {task.status === 'completed' ? 'Mark as Pending' : 'Mark as Completed'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.dangerButton]}
            onPress={handleDelete}
          >
            <Text style={styles.actionButtonText}>Delete Task</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default TaskDetailScreen;
