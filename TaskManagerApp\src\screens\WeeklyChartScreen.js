import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LineChart, BarChart } from 'react-native-chart-kit';
import { useTask } from '../contexts/TaskContext';
import { useTheme } from '../contexts/ThemeContext';

const { width } = Dimensions.get('window');

const WeeklyChartScreen = ({ navigation }) => {
  const { allTasks } = useTask();
  const { theme } = useTheme();
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    navigation.setOptions({
      title: 'Weekly Progress',
      headerStyle: {
        backgroundColor: theme.colors.background,
      },
      headerTintColor: theme.colors.text,
      headerTitleStyle: {
        color: theme.colors.text,
      },
    });
    
    generateChartData();
  }, [navigation, theme, allTasks]);

  const generateChartData = () => {
    // Get the last 7 weeks of data
    const weeks = [];
    const completedCounts = [];
    const createdCounts = [];
    
    for (let i = 6; i >= 0; i--) {
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - (i * 7));
      weekStart.setHours(0, 0, 0, 0);
      
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);
      
      // Count completed tasks in this week
      const completedInWeek = allTasks.filter(task => {
        if (task.status !== 'completed') return false;
        const updatedAt = new Date(task.updated_at);
        return updatedAt >= weekStart && updatedAt <= weekEnd;
      }).length;
      
      // Count created tasks in this week
      const createdInWeek = allTasks.filter(task => {
        const createdAt = new Date(task.created_at);
        return createdAt >= weekStart && createdAt <= weekEnd;
      }).length;
      
      weeks.push(formatWeekLabel(weekStart));
      completedCounts.push(completedInWeek);
      createdCounts.push(createdInWeek);
    }
    
    setChartData({
      labels: weeks,
      datasets: [
        {
          data: completedCounts,
          color: (opacity = 1) => theme.colors.taskCompleted,
          strokeWidth: 3,
        },
      ],
      barData: {
        labels: weeks,
        datasets: [
          {
            data: createdCounts,
            color: (opacity = 1) => theme.colors.primary,
          },
        ],
      },
      completedCounts,
      createdCounts,
    });
  };

  const formatWeekLabel = (date) => {
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const day = date.getDate();
    return `${month} ${day}`;
  };

  const getWeeklyStats = () => {
    if (!chartData) return { totalCompleted: 0, avgPerWeek: 0, bestWeek: 0 };
    
    const totalCompleted = chartData.completedCounts.reduce((sum, count) => sum + count, 0);
    const avgPerWeek = Math.round(totalCompleted / 7);
    const bestWeek = Math.max(...chartData.completedCounts);
    
    return { totalCompleted, avgPerWeek, bestWeek };
  };

  const chartConfig = {
    backgroundColor: theme.colors.background,
    backgroundGradientFrom: theme.colors.background,
    backgroundGradientTo: theme.colors.background,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.colors.primary,
    labelColor: (opacity = 1) => theme.colors.text,
    style: {
      borderRadius: theme.borderRadius.md,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.taskCompleted,
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: theme.colors.border,
      strokeWidth: 1,
    },
  };

  const barChartConfig = {
    ...chartConfig,
    color: (opacity = 1) => theme.colors.primary,
  };

  const stats = getWeeklyStats();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.lg,
    },
    header: {
      marginBottom: theme.spacing.xl,
    },
    title: {
      fontSize: theme.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.xl,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: theme.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.taskCompleted,
      marginBottom: theme.spacing.xs,
    },
    statLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    chartSection: {
      marginBottom: theme.spacing.xl,
    },
    chartTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
      textAlign: 'center',
    },
    chartContainer: {
      alignItems: 'center',
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    noDataContainer: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.xl,
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    noDataText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    legend: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: theme.spacing.md,
    },
    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: theme.spacing.md,
    },
    legendColor: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: theme.spacing.xs,
    },
    legendText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.text,
    },
  });

  if (!chartData) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>
              Loading chart data...
            </Text>
          </View>
        </View>
      </View>
    );
  }

  const hasData = stats.totalCompleted > 0;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>📊 Weekly Progress</Text>
          <Text style={styles.subtitle}>
            Track your task completion over the last 7 weeks
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.totalCompleted}</Text>
            <Text style={styles.statLabel}>Total{'\n'}Completed</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.avgPerWeek}</Text>
            <Text style={styles.statLabel}>Average{'\n'}Per Week</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.bestWeek}</Text>
            <Text style={styles.statLabel}>Best{'\n'}Week</Text>
          </View>
        </View>

        {hasData ? (
          <>
            <View style={styles.chartSection}>
              <Text style={styles.chartTitle}>Tasks Completed</Text>
              <View style={styles.chartContainer}>
                <LineChart
                  data={chartData}
                  width={width - 80}
                  height={220}
                  chartConfig={chartConfig}
                  bezier
                  style={{
                    borderRadius: theme.borderRadius.md,
                  }}
                />
              </View>
            </View>

            <View style={styles.chartSection}>
              <Text style={styles.chartTitle}>Tasks Created</Text>
              <View style={styles.chartContainer}>
                <BarChart
                  data={chartData.barData}
                  width={width - 80}
                  height={220}
                  chartConfig={barChartConfig}
                  style={{
                    borderRadius: theme.borderRadius.md,
                  }}
                />
                <View style={styles.legend}>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: theme.colors.taskCompleted }]} />
                    <Text style={styles.legendText}>Completed</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: theme.colors.primary }]} />
                    <Text style={styles.legendText}>Created</Text>
                  </View>
                </View>
              </View>
            </View>
          </>
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>
              No task data available yet.{'\n'}
              Complete some tasks to see your progress!
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default WeeklyChartScreen;
