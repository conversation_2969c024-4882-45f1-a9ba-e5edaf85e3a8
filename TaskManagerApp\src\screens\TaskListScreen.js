import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTask } from '../contexts/TaskContext';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import TaskItem from '../components/TaskItem';
import FloatingActionButton from '../components/FloatingActionButton';
import TaskStats from '../components/TaskStats';
import FilterSortModal from '../components/FilterSortModal';

const TaskListScreen = ({ navigation }) => {
  const { tasks, loading, error, loadTasks, getTaskStats } = useTask();
  const { signOut } = useAuth();
  const { theme, toggleTheme, isDarkMode } = useTheme();
  const [refreshing, setRefreshing] = useState(false);
  const [showFilterSort, setShowFilterSort] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.surface }]}
            onPress={toggleTheme}
          >
            <Text style={[styles.headerButtonText, { color: theme.colors.text }]}>
              {isDarkMode ? '☀️' : '🌙'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.surface }]}
            onPress={handleSignOut}
          >
            <Text style={[styles.headerButtonText, { color: theme.colors.text }]}>
              Logout
            </Text>
          </TouchableOpacity>
        </View>
      ),
      headerStyle: {
        backgroundColor: theme.colors.background,
      },
      headerTintColor: theme.colors.text,
      headerTitleStyle: {
        color: theme.colors.text,
      },
    });
  }, [navigation, theme, isDarkMode]);

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTasks();
    setRefreshing(false);
  };

  const handleTaskPress = (task) => {
    navigation.navigate('TaskDetail', { task });
  };

  const handleAddTask = () => {
    navigation.navigate('AddEditTask');
  };

  const handleAISuggest = () => {
    navigation.navigate('AISuggest');
  };

  const handleSortTasks = () => {
    setShowFilterSort(true);
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
        No tasks yet
      </Text>
      <Text style={[styles.emptyStateSubtitle, { color: theme.colors.textSecondary }]}>
        Tap the + button to create your first task
      </Text>
    </View>
  );

  const renderTaskItem = ({ item }) => (
    <TaskItem
      task={item}
      onPress={() => handleTaskPress(item)}
    />
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    headerButtons: {
      flexDirection: 'row',
      marginRight: theme.spacing.md,
    },
    headerButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      marginLeft: theme.spacing.xs,
    },
    headerButtonText: {
      fontSize: theme.fontSize.sm,
      fontWeight: '600',
    },
    content: {
      flex: 1,
    },
    list: {
      flex: 1,
      paddingHorizontal: theme.spacing.md,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.xl,
    },
    emptyStateTitle: {
      fontSize: theme.fontSize.xl,
      fontWeight: 'bold',
      marginBottom: theme.spacing.sm,
    },
    emptyStateSubtitle: {
      fontSize: theme.fontSize.md,
      textAlign: 'center',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.xl,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.fontSize.md,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
    },
    retryButtonText: {
      color: theme.colors.fabText,
      fontSize: theme.fontSize.md,
      fontWeight: '600',
    },
  });

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading tasks: {error}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadTasks}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TaskStats />
      
      <View style={styles.content}>
        <FlatList
          style={styles.list}
          data={tasks}
          renderItem={renderTaskItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
              colors={[theme.colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={tasks.length === 0 ? { flex: 1 } : null}
        />
      </View>

      <FloatingActionButton
        onAddTask={handleAddTask}
        onAISuggest={handleAISuggest}
        onSortTasks={handleSortTasks}
      />

      <FilterSortModal
        visible={showFilterSort}
        onClose={() => setShowFilterSort(false)}
      />
    </View>
  );
};

export default TaskListScreen;
