import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTask } from '../contexts/TaskContext';
import { useTheme } from '../contexts/ThemeContext';

const TaskStats = () => {
  const { getTaskStats, filterStatus, setFilterStatus } = useTask();
  const { theme } = useTheme();
  const stats = getTaskStats();

  const statItems = [
    {
      key: 'all',
      label: 'Total',
      value: stats.total,
      color: theme.colors.primary,
    },
    {
      key: 'pending',
      label: 'Pending',
      value: stats.pending,
      color: theme.colors.taskPending,
    },
    {
      key: 'completed',
      label: 'Completed',
      value: stats.completed,
      color: theme.colors.taskCompleted,
    },
    {
      key: 'high',
      label: 'High Priority',
      value: stats.highPriority,
      color: theme.colors.priorityHigh,
    },
    {
      key: 'overdue',
      label: 'Overdue',
      value: stats.overdue,
      color: theme.colors.error,
    },
  ];

  const handleStatPress = (key) => {
    if (key === 'all') {
      setFilterStatus('all');
    } else if (key === 'pending') {
      setFilterStatus(filterStatus === 'pending' ? 'all' : 'pending');
    } else if (key === 'completed') {
      setFilterStatus(filterStatus === 'completed' ? 'all' : 'completed');
    }
  };

  const getCompletionPercentage = () => {
    if (stats.total === 0) return 0;
    return Math.round((stats.completed / stats.total) * 100);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      margin: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    title: {
      fontSize: theme.fontSize.lg,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    completionPercentage: {
      fontSize: theme.fontSize.md,
      fontWeight: '600',
      color: theme.colors.taskCompleted,
    },
    progressBar: {
      height: 4,
      backgroundColor: theme.colors.border,
      borderRadius: 2,
      marginBottom: theme.spacing.md,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: theme.colors.taskCompleted,
      borderRadius: 2,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    statItem: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
      alignItems: 'center',
    },
    statItemActive: {
      borderWidth: 2,
    },
    statValue: {
      fontSize: theme.fontSize.xl,
      fontWeight: 'bold',
      marginBottom: theme.spacing.xs,
    },
    statLabel: {
      fontSize: theme.fontSize.xs,
      fontWeight: '500',
      textAlign: 'center',
      color: theme.colors.textSecondary,
    },
    emptyState: {
      alignItems: 'center',
      paddingVertical: theme.spacing.md,
    },
    emptyStateText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  if (stats.total === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>
            No tasks yet. Create your first task to see statistics!
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Task Overview</Text>
        <Text style={styles.completionPercentage}>
          {getCompletionPercentage()}% Complete
        </Text>
      </View>

      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            { width: `${getCompletionPercentage()}%` },
          ]}
        />
      </View>

      <View style={styles.statsGrid}>
        {statItems.map((item) => {
          const isActive = 
            (item.key === 'pending' && filterStatus === 'pending') ||
            (item.key === 'completed' && filterStatus === 'completed') ||
            (item.key === 'all' && filterStatus === 'all');

          return (
            <TouchableOpacity
              key={item.key}
              style={[
                styles.statItem,
                isActive && styles.statItemActive,
                isActive && { borderColor: item.color },
              ]}
              onPress={() => handleStatPress(item.key)}
              activeOpacity={0.7}
            >
              <Text style={[styles.statValue, { color: item.color }]}>
                {item.value}
              </Text>
              <Text style={styles.statLabel}>{item.label}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default TaskStats;
