import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export const scheduleTaskReminder = async (task) => {
  if (!task.due_date) return;

  const dueDate = new Date(task.due_date);
  const now = new Date();
  
  // Don't schedule notifications for past dates
  if (dueDate <= now) return;

  // Schedule notification 1 hour before due date
  const reminderTime = new Date(dueDate.getTime() - 60 * 60 * 1000);
  
  if (reminderTime <= now) return;

  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Task Reminder',
        body: `"${task.title}" is due in 1 hour`,
        data: { taskId: task.id },
      },
      trigger: {
        date: reminderTime,
      },
    });

    return notificationId;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    return null;
  }
};

export const cancelTaskReminder = async (notificationId) => {
  if (!notificationId) return;
  
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  } catch (error) {
    console.error('Error canceling notification:', error);
  }
};

export const scheduleTaskCompletionCelebration = async () => {
  try {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: '🎉 Task Completed!',
        body: 'Great job! You completed a task.',
      },
      trigger: null, // Show immediately
    });
  } catch (error) {
    console.error('Error showing completion notification:', error);
  }
};

export const scheduleDailyReminder = async () => {
  try {
    // Cancel existing daily reminders
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    const dailyReminders = scheduledNotifications.filter(
      notification => notification.content.data?.type === 'daily_reminder'
    );
    
    for (const reminder of dailyReminders) {
      await Notifications.cancelScheduledNotificationAsync(reminder.identifier);
    }

    // Schedule new daily reminder for 9 AM
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Good Morning! 🌅',
        body: 'Check your tasks for today and plan your day.',
        data: { type: 'daily_reminder' },
      },
      trigger: {
        hour: 9,
        minute: 0,
        repeats: true,
      },
    });
  } catch (error) {
    console.error('Error scheduling daily reminder:', error);
  }
};

export const requestNotificationPermissions = async () => {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  
  return finalStatus === 'granted';
};
