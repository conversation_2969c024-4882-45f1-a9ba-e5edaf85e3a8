
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 * and copied to the cpp directory to override the adopt function and set the size of the shadow node based
 * on the state.
 * @generated by codegen project: GenerateComponentDescriptorH.js
 */

#pragma once

#include "ShadowNodes.h"
#include <react/renderer/core/ConcreteComponentDescriptor.h>

namespace facebook {
namespace react {

class RNDateTimePickerComponentDescriptor final : public ConcreteComponentDescriptor<RNDateTimePickerShadowNode> {
  public:
    using ConcreteComponentDescriptor::ConcreteComponentDescriptor;

    void adopt(ShadowNode& shadowNode) const override {
      auto& pickerShadowNode = static_cast<RNDateTimePickerShadowNode&>(shadowNode);
      auto& layoutableShadowNode = static_cast<YogaLayoutableShadowNode&>(pickerShadowNode);

      auto state = std::static_pointer_cast<const RNDateTimePickerShadowNode::ConcreteState>(shadowNode.getState());
      auto stateData = state->getData();

      if(stateData.frameSize.width != 0 && stateData.frameSize.height != 0) {
        layoutableShadowNode.setSize(Size{stateData.frameSize.width, stateData.frameSize.height});
      }

      ConcreteComponentDescriptor::adopt(shadowNode);
    }
};

} // namespace react
} // namespace facebook
