import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, getTasks, createTask, updateTask, deleteTask, subscribeToTasks } from '../services/supabase';
import { useAuth } from './AuthContext';

const TaskContext = createContext();

export const useTask = () => {
  const context = useContext(TaskContext);
  if (!context) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};

export const TaskProvider = ({ children }) => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('created_at'); // created_at, due_date, priority, title
  const [sortOrder, setSortOrder] = useState('desc'); // asc, desc
  const [filterStatus, setFilterStatus] = useState('all'); // all, pending, completed

  useEffect(() => {
    if (user) {
      loadTasks();
      setupRealtimeSubscription();
    } else {
      setTasks([]);
    }
  }, [user]);

  const loadTasks = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      const { data, error } = await getTasks(user.id);
      
      if (error) throw error;
      
      setTasks(data || []);
    } catch (err) {
      setError(err.message);
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    if (!user) return;

    const subscription = subscribeToTasks(user.id, (payload) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      switch (eventType) {
        case 'INSERT':
          setTasks(prev => [newRecord, ...prev]);
          break;
        case 'UPDATE':
          setTasks(prev => prev.map(task => 
            task.id === newRecord.id ? newRecord : task
          ));
          break;
        case 'DELETE':
          setTasks(prev => prev.filter(task => task.id !== oldRecord.id));
          break;
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  };

  const addTask = async (taskData) => {
    if (!user) return { data: null, error: 'User not authenticated' };
    
    try {
      setError(null);
      const newTask = {
        ...taskData,
        user_id: user.id,
      };
      
      const { data, error } = await createTask(newTask);
      
      if (error) throw error;
      
      return { data, error: null };
    } catch (err) {
      setError(err.message);
      return { data: null, error: err };
    }
  };

  const editTask = async (taskId, updates) => {
    try {
      setError(null);
      const { data, error } = await updateTask(taskId, updates);
      
      if (error) throw error;
      
      return { data, error: null };
    } catch (err) {
      setError(err.message);
      return { data: null, error: err };
    }
  };

  const removeTask = async (taskId) => {
    try {
      setError(null);
      const { data, error } = await deleteTask(taskId);
      
      if (error) throw error;
      
      return { data, error: null };
    } catch (err) {
      setError(err.message);
      return { data: null, error: err };
    }
  };

  const toggleTaskStatus = async (taskId, currentStatus) => {
    const newStatus = currentStatus === 'completed' ? 'pending' : 'completed';
    return await editTask(taskId, { status: newStatus });
  };

  const getSortedAndFilteredTasks = () => {
    let filteredTasks = tasks;
    
    // Apply status filter
    if (filterStatus !== 'all') {
      filteredTasks = tasks.filter(task => task.status === filterStatus);
    }
    
    // Apply sorting
    filteredTasks.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      // Handle priority sorting
      if (sortBy === 'priority') {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
      }
      
      // Handle date sorting
      if (sortBy === 'due_date' || sortBy === 'created_at') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }
      
      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    return filteredTasks;
  };

  const getTaskStats = () => {
    const total = tasks.length;
    const completed = tasks.filter(task => task.status === 'completed').length;
    const pending = tasks.filter(task => task.status === 'pending').length;
    const highPriority = tasks.filter(task => task.priority === 'high').length;
    const overdue = tasks.filter(task => {
      if (!task.due_date || task.status === 'completed') return false;
      return new Date(task.due_date) < new Date();
    }).length;
    
    return { total, completed, pending, highPriority, overdue };
  };

  const value = {
    tasks: getSortedAndFilteredTasks(),
    allTasks: tasks,
    loading,
    error,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    filterStatus,
    setFilterStatus,
    addTask,
    editTask,
    removeTask,
    toggleTaskStatus,
    loadTasks,
    getTaskStats,
  };

  return (
    <TaskContext.Provider value={value}>
      {children}
    </TaskContext.Provider>
  );
};
