{"name": "@react-native-community/datetimepicker", "version": "8.4.1", "description": "DateTimePicker component for React Native", "main": "./src/index.js", "types": "src/index.d.ts", "files": ["ios", "android", "src", "jest", "flow-typed", "windows", "RNDateTimePicker.podspec", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "plugin/build", "app.plugin.js"], "publishConfig": {"access": "public"}, "scripts": {"start": "patch-package && react-native start", "start:android": "react-native run-android", "start:ios": "react-native run-ios", "start:windows": "react-native run-windows --sln example/windows/date-time-picker-example.sln", "bundle:android": "mkdir -p example/dist && react-native bundle --platform android --dev false --entry-file index.js --bundle-output example/dist/main.android.jsbundle --assets-dest example/dist/res", "bundle:ios": "mkdir -p example/dist && react-native bundle --platform ios --dev false --entry-file index.js --bundle-output example/dist/main.ios.jsbundle --assets-dest example/dist/assets", "test": "jest", "lint": "NODE_ENV=lint eslint {example,src,test}/**/*.js src/index.d.ts", "flow": "flow check", "detox:ios:build:debug": "detox build -c ios.sim.debug", "detox:ios:test:debug": "SIMCTL_CHILD_TZ=Europe/Prague detox test -c ios.sim.debug -l verbose", "detox:ios:build:release": "detox build -c ios.sim.release", "detox:ios:test:release": "SIMCTL_CHILD_TZ=Europe/Prague detox test -c ios.sim.release --record-videos all --record-logs all -l verbose", "detox:android:build:debug": "ORG_GRADLE_PROJECT_newArchEnabled=false detox build -c android.emu.debug", "detox:android:test:debug": "adb shell service call alarm 3 s16 Europe/Prague && detox test -c android.emu.debug -l verbose", "detox:android:build:release": "ORG_GRADLE_PROJECT_newArchEnabled=false detox build -c android.emu.release", "detox:android:test:release": "adb shell service call alarm 3 s16 Europe/Prague && detox test -c android.emu.release --record-videos all --record-logs all --headless -l verbose", "detox:clean": "rimraf example/android/build && rimraf example/android/app/build && rimraf example/android/.gradle && rimraf example/ios/build", "plugin:build": "expo-module build plugin", "generateManifest": "node node_modules/react-native-test-app/android/android-manifest.js example/app.json example/android/app/build/generated/rnta/src/main/AndroidManifest.xml"}, "repository": {"type": "git", "url": "git+https://github.com/react-native-community/datetimepicker.git"}, "keywords": ["react-native-component", "react-native", "ios", "android", "windows", "datepicker", "timepicker", "datetime"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/swaagie)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/Daniel<PERSON>anudo)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vonovak)", "<PERSON> <<EMAIL>> (https://github.com/lochness42)"], "license": "MIT", "bugs": {"url": "https://github.com/react-native-community/datetimepicker/issues"}, "homepage": "https://github.com/react-native-community/datetimepicker#readme", "devDependencies": {"@callstack/react-native-visionos": "^0.75.0", "@react-native-segmented-control/segmented-control": "^2.5.6", "@react-native/eslint-config": "^0.75.4", "@react-native/metro-config": "^0.75.4", "@rnx-kit/metro-config": "^1.3.15", "@semantic-release/git": "^10.0.1", "@testing-library/react-native": "9.1.0", "babel-jest": "^29.5.0", "detox": "^20.28.0", "eslint": "^8.56.0", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-prettier": "^4.2.1", "expo": "^52.0.30", "expo-module-scripts": "^3.5.2", "flow-bin": "^0.217.0", "flow-typed": "^3.9.0", "jest": "^29.5.0", "moment": "^2.24.0", "moment-timezone": "^0.5.41", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^2.8.8", "react": "18.3.1", "react-native": "^0.75.4", "react-native-localize": "^3.1.0", "react-native-test-app": "^4.0.7", "react-native-windows": "^0.75.10", "react-test-renderer": "18.3.1", "semantic-release": "^19.0.3", "typescript": "^5.5.3"}, "dependencies": {"invariant": "^2.2.4"}, "peerDependencies": {"expo": ">=52.0.0", "react": "*", "react-native": "*", "react-native-windows": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}, "react-native-windows": {"optional": true}}, "codegenConfig": {"name": "RNDateTimePickerCGen", "type": "all", "jsSrcsDir": "src/specs", "android": {"javaPackageName": "com.reactcommunity.rndatetimepicker"}}, "packageManager": "yarn@4.1.1"}