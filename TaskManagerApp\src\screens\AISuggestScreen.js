import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTask } from '../contexts/TaskContext';
import { useTheme } from '../contexts/ThemeContext';
import { getAITaskSuggestion } from '../services/supabase';

const AISuggestScreen = ({ navigation }) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const { addTask } = useTask();
  const { theme } = useTheme();

  // Fallback suggestions when AI service is not available
  const fallbackSuggestions = [
    {
      title: 'Review weekly goals',
      description: 'Take 15 minutes to review and adjust your weekly objectives',
      priority: 'medium',
    },
    {
      title: 'Clean workspace',
      description: 'Organize your desk and digital workspace for better productivity',
      priority: 'low',
    },
    {
      title: 'Plan tomorrow\'s priorities',
      description: 'Identify the top 3 tasks for tomorrow before ending today',
      priority: 'high',
    },
    {
      title: 'Take a break',
      description: 'Step away from work for 10-15 minutes to recharge',
      priority: 'medium',
    },
    {
      title: 'Update project status',
      description: 'Send progress updates to team members or stakeholders',
      priority: 'medium',
    },
    {
      title: 'Learn something new',
      description: 'Spend 20 minutes learning a new skill or reading industry news',
      priority: 'low',
    },
  ];

  useEffect(() => {
    navigation.setOptions({
      title: 'AI Task Suggestions',
      headerStyle: {
        backgroundColor: theme.colors.background,
      },
      headerTintColor: theme.colors.text,
      headerTitleStyle: {
        color: theme.colors.text,
      },
    });
    
    generateSuggestions();
  }, [navigation, theme]);

  const generateSuggestions = async () => {
    setLoading(true);
    try {
      // Try to get AI suggestions first
      const { data, error } = await getAITaskSuggestion();
      
      if (error || !data) {
        // Use fallback suggestions if AI service is not available
        const randomSuggestions = getRandomSuggestions(3);
        setSuggestions(randomSuggestions);
      } else {
        setSuggestions(data.suggestions || []);
      }
    } catch (error) {
      console.log('AI service not available, using fallback suggestions');
      const randomSuggestions = getRandomSuggestions(3);
      setSuggestions(randomSuggestions);
    } finally {
      setLoading(false);
    }
  };

  const getRandomSuggestions = (count) => {
    const shuffled = [...fallbackSuggestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  const handleAddSuggestion = async (suggestion) => {
    try {
      const { error } = await addTask({
        title: suggestion.title,
        description: suggestion.description,
        priority: suggestion.priority,
      });

      if (error) {
        Alert.alert('Error', error.message);
      } else {
        Alert.alert(
          'Success',
          'Task added successfully!',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return theme.colors.priorityHigh;
      case 'medium':
        return theme.colors.priorityMedium;
      case 'low':
        return theme.colors.priorityLow;
      default:
        return theme.colors.textSecondary;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.lg,
    },
    header: {
      marginBottom: theme.spacing.xl,
    },
    title: {
      fontSize: theme.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      lineHeight: 22,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: theme.spacing.md,
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
    },
    suggestionCard: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.md,
      borderLeftWidth: 4,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    suggestionTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    suggestionDescription: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: theme.spacing.md,
    },
    suggestionFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    priorityBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    priorityText: {
      fontSize: theme.fontSize.xs,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
    },
    addButtonText: {
      color: theme.colors.fabText,
      fontSize: theme.fontSize.sm,
      fontWeight: '600',
    },
    regenerateButton: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    regenerateButtonText: {
      color: theme.colors.primary,
      fontSize: theme.fontSize.md,
      fontWeight: '600',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.xl,
    },
    emptyStateText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Generating AI suggestions...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>🤖 AI Task Suggestions</Text>
          <Text style={styles.subtitle}>
            Here are some personalized task suggestions to boost your productivity.
            Tap "Add Task" to add any suggestion to your task list.
          </Text>
        </View>

        {suggestions.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No suggestions available at the moment. Try regenerating suggestions.
            </Text>
          </View>
        ) : (
          suggestions.map((suggestion, index) => (
            <View
              key={index}
              style={[
                styles.suggestionCard,
                { borderLeftColor: getPriorityColor(suggestion.priority) },
              ]}
            >
              <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
              <Text style={styles.suggestionDescription}>
                {suggestion.description}
              </Text>
              <View style={styles.suggestionFooter}>
                <View
                  style={[
                    styles.priorityBadge,
                    { backgroundColor: getPriorityColor(suggestion.priority) + '20' },
                  ]}
                >
                  <Text
                    style={[
                      styles.priorityText,
                      { color: getPriorityColor(suggestion.priority) },
                    ]}
                  >
                    {suggestion.priority}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => handleAddSuggestion(suggestion)}
                >
                  <Text style={styles.addButtonText}>Add Task</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}

        <TouchableOpacity
          style={styles.regenerateButton}
          onPress={generateSuggestions}
          disabled={loading}
        >
          <Text style={styles.regenerateButtonText}>
            {loading ? 'Generating...' : '🔄 Generate New Suggestions'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default AISuggestScreen;
