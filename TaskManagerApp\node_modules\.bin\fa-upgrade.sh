#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -x "$basedir//bin/sh" ]; then
  exec "$basedir//bin/sh"  "$basedir/../react-native-vector-icons/bin/fa-upgrade.sh" "$@"
else 
  exec /bin/sh  "$basedir/../react-native-vector-icons/bin/fa-upgrade.sh" "$@"
fi
