{"version": 3, "sources": ["jestUtils.ts"], "names": ["fireEvent", "_element", "_name", "_data", "require", "_e", "handlersDefaultEvents", "flingHandlerName", "x", "y", "absoluteX", "absoluteY", "numberOfPointers", "forceTouchHandlerName", "force", "longPressHandlerName", "duration", "nativeViewHandlerName", "pointerInside", "panHandlerName", "translationX", "translationY", "velocityX", "velocityY", "stylusData", "undefined", "pinchHandlerName", "focalX", "focalY", "scale", "velocity", "rotationHandlerName", "anchorX", "anchorY", "rotation", "tapHandlerName", "isGesture", "componentOrGesture", "BaseGesture", "wrapWithNativeEvent", "event", "nativeEvent", "fillOldStateChanges", "previousEvent", "currentEvent", "isFirstEvent", "oldState", "State", "UNDETERMINED", "isGestureStateEvent", "state", "validateStateTransitions", "stringify", "JSON", "errorMsgWithBothEvents", "description", "errorMsgWithCurrentEvent", "BEGAN", "fillMissingDefaultsFor", "handlerType", "handlerTag", "isDiscreteHandler", "fillMissingStatesTransitions", "events", "_events", "lastEvent", "length", "firstEvent", "shouldDuplicateFirstEvent", "hasState", "duplicated", "unshift", "shouldDuplicateLastEvent", "END", "FAILED", "CANCELLED", "push", "isWithoutState", "noEventsLeft", "trueFn", "fillEventsForCurrentState", "shouldConsumeEvent", "shouldTransitionToNextState", "peekCurrentEvent", "peekNextEvent", "consumeCurrentEvent", "shift", "nextEvent", "currentRequiredState", "REQUIRED_EVENTS", "currentStateIdx", "eventData", "shouldUseEvent", "transformedEvents", "ACTIVE", "hasAllStates", "iterations", "nextRequiredState", "e", "getHandlerData", "gesture", "emitEvent", "eventName", "args", "DeviceEventEmitter", "emit", "handler<PERSON>ame", "enabled", "config", "gestureHandlerComponent", "props", "fireGestureHandler", "eventList", "_", "map", "lastSentEvent", "hasChangedState", "getByGestureTestId", "testID", "handler", "Error"], "mappings": ";;;;;;;;AAAA;;AACA;;AAEA;;AAIA;;AAWA;;AAOA;;AACA;;AAcA;;AAIA;;AAIA;;AAIA;;AAIA;;AAIA;;AACA;;;;AAEA;AACA,IAAIA,SAAS,GAAG,CACdC,QADc,EAEdC,KAFc,EAGd,GAAGC,KAHW,KAIX,CACH;AACD,CAND;;AAQA,IAAI;AACF;AACAH,EAAAA,SAAS,GAAGI,OAAO,CAAC,+BAAD,CAAP,CAAyCJ,SAArD;AACD,CAHD,CAGE,OAAOK,EAAP,EAAW,CACX;AACD;;AAyBD,MAAMC,qBAA2C,GAAG;AAClD,GAACC,qCAAD,GAAoB;AAClBC,IAAAA,CAAC,EAAE,CADe;AAElBC,IAAAA,CAAC,EAAE,CAFe;AAGlBC,IAAAA,SAAS,EAAE,CAHO;AAIlBC,IAAAA,SAAS,EAAE,CAJO;AAKlBC,IAAAA,gBAAgB,EAAE;AALA,GAD8B;AAQlD,GAACC,+CAAD,GAAyB;AACvBL,IAAAA,CAAC,EAAE,CADoB;AAEvBC,IAAAA,CAAC,EAAE,CAFoB;AAGvBC,IAAAA,SAAS,EAAE,CAHY;AAIvBC,IAAAA,SAAS,EAAE,CAJY;AAKvBG,IAAAA,KAAK,EAAE,CALgB;AAMvBF,IAAAA,gBAAgB,EAAE;AANK,GARyB;AAgBlD,GAACG,6CAAD,GAAwB;AACtBP,IAAAA,CAAC,EAAE,CADmB;AAEtBC,IAAAA,CAAC,EAAE,CAFmB;AAGtBC,IAAAA,SAAS,EAAE,CAHW;AAItBC,IAAAA,SAAS,EAAE,CAJW;AAKtBK,IAAAA,QAAQ,EAAE,GALY;AAMtBJ,IAAAA,gBAAgB,EAAE;AANI,GAhB0B;AAwBlD,GAACK,+CAAD,GAAyB;AACvBC,IAAAA,aAAa,EAAE,IADQ;AAEvBN,IAAAA,gBAAgB,EAAE;AAFK,GAxByB;AA4BlD,GAACO,iCAAD,GAAkB;AAChBX,IAAAA,CAAC,EAAE,CADa;AAEhBC,IAAAA,CAAC,EAAE,CAFa;AAGhBC,IAAAA,SAAS,EAAE,CAHK;AAIhBC,IAAAA,SAAS,EAAE,CAJK;AAKhBS,IAAAA,YAAY,EAAE,GALE;AAMhBC,IAAAA,YAAY,EAAE,CANE;AAOhBC,IAAAA,SAAS,EAAE,CAPK;AAQhBC,IAAAA,SAAS,EAAE,CARK;AAShBX,IAAAA,gBAAgB,EAAE,CATF;AAUhBY,IAAAA,UAAU,EAAEC;AAVI,GA5BgC;AAwClD,GAACC,qCAAD,GAAoB;AAClBC,IAAAA,MAAM,EAAE,CADU;AAElBC,IAAAA,MAAM,EAAE,CAFU;AAGlBC,IAAAA,KAAK,EAAE,CAHW;AAIlBC,IAAAA,QAAQ,EAAE,CAJQ;AAKlBlB,IAAAA,gBAAgB,EAAE;AALA,GAxC8B;AA+ClD,GAACmB,2CAAD,GAAuB;AACrBC,IAAAA,OAAO,EAAE,CADY;AAErBC,IAAAA,OAAO,EAAE,CAFY;AAGrBC,IAAAA,QAAQ,EAAE,IAHW;AAIrBJ,IAAAA,QAAQ,EAAE,CAJW;AAKrBlB,IAAAA,gBAAgB,EAAE;AALG,GA/C2B;AAsDlD,GAACuB,iCAAD,GAAkB;AAChB3B,IAAAA,CAAC,EAAE,CADa;AAEhBC,IAAAA,CAAC,EAAE,CAFa;AAGhBC,IAAAA,SAAS,EAAE,CAHK;AAIhBC,IAAAA,SAAS,EAAE,CAJK;AAKhBC,IAAAA,gBAAgB,EAAE;AALF;AAtDgC,CAApD;;AA+DA,SAASwB,SAAT,CACEC,kBADF,EAEqC;AACnC,SAAOA,kBAAkB,YAAYC,oBAArC;AACD;;AAKD,SAASC,mBAAT,CACEC,KADF,EAEkC;AAChC,SAAO;AAAEC,IAAAA,WAAW,EAAED;AAAf,GAAP;AACD;;AAED,SAASE,mBAAT,CACEC,aADF,EAEEC,YAFF,EAG2B;AACzB,QAAMC,YAAY,GAAGF,aAAa,KAAK,IAAvC;;AACA,MAAIE,YAAJ,EAAkB;AAChB,WAAO;AACLC,MAAAA,QAAQ,EAAEC,aAAMC,YADX;AAEL,SAAGJ;AAFE,KAAP;AAID;;AAED,QAAMK,mBAAmB,GAAGN,aAAa,CAACO,KAAd,KAAwBN,YAAY,CAACM,KAAjE;;AACA,MAAID,mBAAJ,EAAyB;AACvB,WAAO;AACLH,MAAAA,QAAQ,EAAEH,aAAF,aAAEA,aAAF,uBAAEA,aAAa,CAAEO,KADpB;AAEL,SAAGN;AAFE,KAAP;AAID,GALD,MAKO;AACL,WAAOA,YAAP;AACD;AACF;;AAKD,SAASO,wBAAT,CACER,aADF,EAEEC,YAFF,EAGE;AACA,WAASQ,SAAT,CAAmBZ,KAAnB,EAA0D;AACxD,WAAOa,IAAI,CAACD,SAAL,CAAeZ,KAAf,EAAsB,IAAtB,EAA4B,CAA5B,CAAP;AACD;;AACD,WAASc,sBAAT,CAAgCC,WAAhC,EAAqD;AACnD,WAAQ,GAAEA,WAAY,oBAAmBH,SAAS,CAChDR,YADgD,CAEhD,qBAAoBQ,SAAS,CAACT,aAAD,CAAgB,EAF/C;AAGD;;AAED,WAASa,wBAAT,CAAkCD,WAAlC,EAAuD;AACrD,WAAQ,GAAEA,WAAY,oBAAmBH,SAAS,CAACR,YAAD,CAAe,EAAjE;AACD;;AAED,0BACE,wBAAYA,YAAZ,EAA0B,OAA1B,CADF,EAEEY,wBAAwB,CAAC,6BAAD,CAF1B;AAKA,QAAMX,YAAY,GAAGF,aAAa,KAAK,IAAvC;;AACA,MAAIE,YAAJ,EAAkB;AAChB,4BACED,YAAY,CAACM,KAAb,KAAuBH,aAAMU,KAD/B,EAEED,wBAAwB,CAAC,mCAAD,CAF1B;AAID;;AAED,MAAIb,aAAa,KAAK,IAAtB,EAA4B;AAC1B,QAAIA,aAAa,CAACO,KAAd,KAAwBN,YAAY,CAACM,KAAzC,EAAgD;AAC9C,8BACE,wBAAYN,YAAZ,EAA0B,UAA1B,CADF,EAEEY,wBAAwB,CACtB,sDADsB,CAF1B;AAMA,8BACEZ,YAAY,CAACE,QAAb,KAA0BH,aAAa,CAACO,KAD1C,EAEEI,sBAAsB,CACpB,0EADoB,CAFxB;AAMD;AACF;;AAED,SAAOV,YAAP;AACD;;AAOD,SAASc,sBAAT,CAAgC;AAC9BC,EAAAA,WAD8B;AAE9BC,EAAAA;AAF8B,CAAhC,EAKwB;AACtB,SAAQpB,KAAD,IAAW;AAChB,WAAO,EACL,GAAGlC,qBAAqB,CAACqD,WAAD,CADnB;AAEL,SAAGnB,KAFE;AAGLoB,MAAAA;AAHK,KAAP;AAKD,GAND;AAOD;;AAED,SAASC,iBAAT,CAA2BF,WAA3B,EAAsD;AACpD,SACEA,WAAW,KAAK,mBAAhB,IACAA,WAAW,KAAK,yBAFlB;AAID;;AAED,SAASG,4BAAT,CACEC,MADF,EAEEF,iBAFF,EAGwB;AAAA;;AAEtB,QAAMG,OAAO,GAAG,CAAC,GAAGD,MAAJ,CAAhB;AACA,QAAME,SAAS,eAAGD,OAAO,CAACA,OAAO,CAACE,MAAR,GAAiB,CAAlB,CAAV,+CAAkC,IAAjD;AACA,QAAMC,UAAU,eAAGH,OAAO,CAAC,CAAD,CAAV,+CAAiB,IAAjC;AAEA,QAAMI,yBAAyB,GAC7B,CAACP,iBAAD,IAAsB,CAACQ,QAAQ,CAACtB,aAAMU,KAAP,CAAR,CAAsBU,UAAtB,CADzB;;AAEA,MAAIC,yBAAJ,EAA+B;AAC7B,UAAME,UAAU,GAAG,EAAE,GAAGH,UAAL;AAAiBjB,MAAAA,KAAK,EAAEH,aAAMU;AAA9B,KAAnB,CAD6B,CAE7B;;AACA,WAAOa,UAAU,CAACxB,QAAlB;;AACAkB,IAAAA,OAAO,CAACO,OAAR,CAAgBD,UAAhB;AACD;;AAED,QAAME,wBAAwB,GAC5B,CAACH,QAAQ,CAACtB,aAAM0B,GAAP,CAAR,CAAoBR,SAApB,CAAD,IACA,CAACI,QAAQ,CAACtB,aAAM2B,MAAP,CAAR,CAAuBT,SAAvB,CADD,IAEA,CAACI,QAAQ,CAACtB,aAAM4B,SAAP,CAAR,CAA0BV,SAA1B,CAHH;;AAKA,MAAIO,wBAAJ,EAA8B;AAC5B,UAAMF,UAAU,GAAG,EAAE,GAAGL,SAAL;AAAgBf,MAAAA,KAAK,EAAEH,aAAM0B;AAA7B,KAAnB,CAD4B,CAE5B;;AACA,WAAOH,UAAU,CAACxB,QAAlB;;AACAkB,IAAAA,OAAO,CAACY,IAAR,CAAaN,UAAb;AACD;;AAED,WAASO,cAAT,CAAwBrC,KAAxB,EAAsC;AACpC,WAAOA,KAAK,KAAK,IAAV,IAAkB,CAAC,wBAAYA,KAAZ,EAAmB,OAAnB,CAA1B;AACD;;AACD,WAAS6B,QAAT,CAAkBnB,KAAlB,EAAgC;AAC9B,WAAQV,KAAD,IAAkBA,KAAK,KAAK,IAAV,IAAkBA,KAAK,CAACU,KAAN,KAAgBA,KAA3D;AACD;;AACD,WAAS4B,YAAT,CAAsBtC,KAAtB,EAAoC;AAClC,WAAOA,KAAK,KAAK,IAAjB;AACD;;AAED,WAASuC,MAAT,GAAkB;AAChB,WAAO,IAAP;AACD;;AAKD,WAASC,yBAAT,CAAmC;AACjCC,IAAAA,kBAAkB,GAAGF,MADY;AAEjCG,IAAAA,2BAA2B,GAAGH;AAFG,GAAnC,EAGS;AACP,aAASI,gBAAT,GAAmC;AAAA;;AACjC,0BAAOnB,OAAO,CAAC,CAAD,CAAd,iDAAqB,IAArB;AACD;;AACD,aAASoB,aAAT,GAAgC;AAAA;;AAC9B,0BAAOpB,OAAO,CAAC,CAAD,CAAd,iDAAqB,IAArB;AACD;;AACD,aAASqB,mBAAT,GAA+B;AAC7BrB,MAAAA,OAAO,CAACsB,KAAR;AACD;;AACD,UAAM1C,YAAY,GAAGuC,gBAAgB,EAArC;AACA,UAAMI,SAAS,GAAGH,aAAa,EAA/B;AACA,UAAMI,oBAAoB,GAAGC,eAAe,CAACC,eAAD,CAA5C;AAEA,QAAIC,SAAS,GAAG,EAAhB;AACA,UAAMC,cAAc,GAAGX,kBAAkB,CAACrC,YAAD,CAAzC;;AACA,QAAIgD,cAAJ,EAAoB;AAClBD,MAAAA,SAAS,GAAG/C,YAAZ;AACAyC,MAAAA,mBAAmB;AACpB;;AACDQ,IAAAA,iBAAiB,CAACjB,IAAlB,CAAuB;AAAE1B,MAAAA,KAAK,EAAEsC,oBAAT;AAA+B,SAAGG;AAAlC,KAAvB;;AACA,QAAIT,2BAA2B,CAACK,SAAD,CAA/B,EAA4C;AAC1CG,MAAAA,eAAe;AAChB;AACF;;AAED,QAAMD,eAAe,GAAG,CAAC1C,aAAMU,KAAP,EAAcV,aAAM+C,MAApB,EAA4B/C,aAAM0B,GAAlC,CAAxB;AAEA,MAAIiB,eAAe,GAAG,CAAtB;AACA,QAAMG,iBAAuC,GAAG,EAAhD;AACA,MAAIE,YAAJ;AACA,MAAIC,UAAU,GAAG,CAAjB;;AACA,KAAG;AACD,UAAMC,iBAAiB,GAAGR,eAAe,CAACC,eAAD,CAAzC;;AACA,QAAIO,iBAAiB,KAAKlD,aAAMU,KAAhC,EAAuC;AACrCuB,MAAAA,yBAAyB,CAAC;AACxBC,QAAAA,kBAAkB,EAAGiB,CAAD,IAClBrB,cAAc,CAACqB,CAAD,CAAd,IAAqB7B,QAAQ,CAACtB,aAAMU,KAAP,CAAR,CAAsByC,CAAtB;AAFC,OAAD,CAAzB;AAID,KALD,MAKO,IAAID,iBAAiB,KAAKlD,aAAM+C,MAAhC,EAAwC;AAC7C,YAAMb,kBAAkB,GAAIiB,CAAD,IACzBrB,cAAc,CAACqB,CAAD,CAAd,IAAqB7B,QAAQ,CAACtB,aAAM+C,MAAP,CAAR,CAAuBI,CAAvB,CADvB;;AAEA,YAAMhB,2BAA2B,GAAIK,SAAD,IAClCT,YAAY,CAACS,SAAD,CAAZ,IACAlB,QAAQ,CAACtB,aAAM0B,GAAP,CAAR,CAAoBc,SAApB,CADA,IAEAlB,QAAQ,CAACtB,aAAM2B,MAAP,CAAR,CAAuBa,SAAvB,CAFA,IAGAlB,QAAQ,CAACtB,aAAM4B,SAAP,CAAR,CAA0BY,SAA1B,CAJF;;AAMAP,MAAAA,yBAAyB,CAAC;AACxBC,QAAAA,kBADwB;AAExBC,QAAAA;AAFwB,OAAD,CAAzB;AAID,KAbM,MAaA,IAAIe,iBAAiB,KAAKlD,aAAM0B,GAAhC,EAAqC;AAC1CO,MAAAA,yBAAyB,CAAC,EAAD,CAAzB;AACD;;AACDe,IAAAA,YAAY,GAAGL,eAAe,KAAKD,eAAe,CAACvB,MAAnD;AAEA,4BACE8B,UAAU,MAAM,GADlB,EAEE,+FAFF;AAID,GA7BD,QA6BS,CAACD,YA7BV;;AA+BA,SAAOF,iBAAP;AACD;;AAYD,SAASM,cAAT,CACE9D,kBADF,EAEe;AACb,MAAID,SAAS,CAACC,kBAAD,CAAb,EAAmC;AACjC,UAAM+D,OAAO,GAAG/D,kBAAhB;AACA,WAAO;AACLgE,MAAAA,SAAS,EAAE,CAACC,SAAD,EAAYC,IAAZ,KAAqB;AAC9BC,wCAAmBC,IAAnB,CAAwBH,SAAxB,EAAmCC,IAAI,CAAC9D,WAAxC;AACD,OAHI;AAILkB,MAAAA,WAAW,EAAEyC,OAAO,CAACM,WAJhB;AAKL9C,MAAAA,UAAU,EAAEwC,OAAO,CAACxC,UALf;AAML+C,MAAAA,OAAO,EAAEP,OAAO,CAACQ,MAAR,CAAeD;AANnB,KAAP;AAQD;;AACD,QAAME,uBAAuB,GAAGxE,kBAAhC;AACA,SAAO;AACLgE,IAAAA,SAAS,EAAE,CAACC,SAAD,EAAYC,IAAZ,KAAqB;AAC9BvG,MAAAA,SAAS,CAAC6G,uBAAD,EAA0BP,SAA1B,EAAqCC,IAArC,CAAT;AACD,KAHI;AAIL5C,IAAAA,WAAW,EAAEkD,uBAAuB,CAACC,KAAxB,CAA8BnD,WAJtC;AAKLC,IAAAA,UAAU,EAAEiD,uBAAuB,CAACC,KAAxB,CAA8BlD,UALrC;AAML+C,IAAAA,OAAO,EAAEE,uBAAuB,CAACC,KAAxB,CAA8BH;AANlC,GAAP;AAQD;;AAkCM,SAASI,kBAAT,CACL1E,kBADK,EAEL2E,SAAsE,GAAG,EAFpE,EAGC;AACN,QAAM;AAAEX,IAAAA,SAAF;AAAa1C,IAAAA,WAAb;AAA0BC,IAAAA,UAA1B;AAAsC+C,IAAAA;AAAtC,MACJR,cAAc,CAAC9D,kBAAD,CADhB;;AAGA,MAAIsE,OAAO,KAAK,KAAhB,EAAuB;AACrB;AACD;;AAED,MAAIM,CAAC,GAAGnD,4BAA4B,CAClCkD,SADkC,EAElCnD,iBAAiB,CAACF,WAAD,CAFiB,CAApC;;AAIAsD,EAAAA,CAAC,GAAGA,CAAC,CAACC,GAAF,CAAMxD,sBAAsB,CAAC;AAAEE,IAAAA,UAAF;AAAcD,IAAAA;AAAd,GAAD,CAA5B,CAAJ;AACAsD,EAAAA,CAAC,GAAG,+BAAmBA,CAAnB,EAAsBvE,mBAAtB,CAAJ;AACAuE,EAAAA,CAAC,GAAG,+BAAmBA,CAAnB,EAAsB9D,wBAAtB,CAAJ,CAdM,CAeN;;AACA8D,EAAAA,CAAC,GAAGA,CAAC,CAACC,GAAF,CAAM3E,mBAAN,CAAJ;AAEA,QAAMwB,MAAM,GAAGkD,CAAf;AAEA,QAAM9C,UAAU,GAAGJ,MAAM,CAACuB,KAAP,EAAnB;AAEAe,EAAAA,SAAS,CAAC,6BAAD,EAAgClC,UAAhC,CAAT;AACA,MAAIgD,aAAa,GAAGhD,UAApB;;AACA,OAAK,MAAM3B,KAAX,IAAoBuB,MAApB,EAA4B;AAC1B,UAAMqD,eAAe,GACnBD,aAAa,CAAC1E,WAAd,CAA0BS,KAA1B,KAAoCV,KAAK,CAACC,WAAN,CAAkBS,KADxD;;AAGA,QAAIkE,eAAJ,EAAqB;AACnBf,MAAAA,SAAS,CAAC,6BAAD,EAAgC7D,KAAhC,CAAT;AACD,KAFD,MAEO;AACL6D,MAAAA,SAAS,CAAC,uBAAD,EAA0B7D,KAA1B,CAAT;AACD;;AACD2E,IAAAA,aAAa,GAAG3E,KAAhB;AACD;AACF;;AAEM,SAAS6E,kBAAT,CAA4BC,MAA5B,EAA4C;AACjD,QAAMC,OAAO,GAAG,2CAAoBD,MAApB,CAAhB;;AACA,MAAIC,OAAO,KAAK,IAAhB,EAAsB;AACpB,UAAM,IAAIC,KAAJ,CAAW,qBAAoBF,MAAO,mBAAtC,CAAN;AACD;;AACD,SAAOC,OAAP;AACD", "sourcesContent": ["import invariant from 'invariant';\nimport { DeviceEventEmitter } from 'react-native';\nimport { ReactTestInstance } from 'react-test-renderer';\nimport {\n  FlingGestureHandler,\n  flingHandlerName,\n} from '../handlers/FlingGestureHandler';\nimport {\n  ForceTouchGestureHandler,\n  forceTouchHandlerName,\n} from '../handlers/ForceTouchGestureHandler';\nimport {\n  BaseGestureHandlerProps,\n  GestureEvent,\n  HandlerStateChangeEvent,\n} from '../handlers/gestureHandlerCommon';\nimport { FlingGesture } from '../handlers/gestures/flingGesture';\nimport { ForceTouchGesture } from '../handlers/gestures/forceTouchGesture';\nimport { BaseGesture, GestureType } from '../handlers/gestures/gesture';\nimport { LongPressGesture } from '../handlers/gestures/longPressGesture';\nimport { NativeGesture } from '../handlers/gestures/nativeGesture';\nimport { PanGesture } from '../handlers/gestures/panGesture';\nimport { PinchGesture } from '../handlers/gestures/pinchGesture';\nimport { RotationGesture } from '../handlers/gestures/rotationGesture';\nimport { TapGesture } from '../handlers/gestures/tapGesture';\nimport { findHandlerByTestID } from '../handlers/handlersRegistry';\nimport {\n  LongPressGestureHandler,\n  longPressHandlerName,\n} from '../handlers/LongPressGestureHandler';\nimport type {\n  FlingGestureHandlerEventPayload,\n  ForceTouchGestureHandlerEventPayload,\n  LongPressGestureHandlerEventPayload,\n  NativeViewGestureHandlerPayload,\n  PanGestureHandlerEventPayload,\n  PinchGestureHandlerEventPayload,\n  RotationGestureHandlerEventPayload,\n  TapGestureHandlerEventPayload,\n} from '../handlers/GestureHandlerEventPayload';\nimport {\n  NativeViewGestureHandler,\n  nativeViewHandlerName,\n} from '../handlers/NativeViewGestureHandler';\nimport {\n  PanGestureHandler,\n  panHandlerName,\n} from '../handlers/PanGestureHandler';\nimport {\n  PinchGestureHandler,\n  pinchHandlerName,\n} from '../handlers/PinchGestureHandler';\nimport {\n  RotationGestureHandler,\n  rotationHandlerName,\n} from '../handlers/RotationGestureHandler';\nimport {\n  TapGestureHandler,\n  tapHandlerName,\n} from '../handlers/TapGestureHandler';\nimport { State } from '../State';\nimport { hasProperty, withPrevAndCurrent } from '../utils';\n\n// Load fireEvent conditionally, so RNGH may be used in setups without testing-library\nlet fireEvent = (\n  _element: ReactTestInstance,\n  _name: string,\n  ..._data: any[]\n) => {\n  // NOOP\n};\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  fireEvent = require('@testing-library/react-native').fireEvent;\n} catch (_e) {\n  // Do nothing if not available\n}\n\ntype GestureHandlerTestEvent<\n  TEventPayload extends Record<string, unknown> = Record<string, unknown>,\n> = (\n  | GestureEvent<TEventPayload>\n  | HandlerStateChangeEvent<TEventPayload>\n)['nativeEvent'];\n\ntype HandlerNames = keyof DefaultEventsMapping;\n\ntype WithNumberOfPointers<T> = {\n  [P in keyof T]: T[P] & { numberOfPointers: number };\n};\ntype DefaultEventsMapping = WithNumberOfPointers<{\n  [flingHandlerName]: FlingGestureHandlerEventPayload;\n  [forceTouchHandlerName]: ForceTouchGestureHandlerEventPayload;\n  [longPressHandlerName]: LongPressGestureHandlerEventPayload;\n  [nativeViewHandlerName]: NativeViewGestureHandlerPayload;\n  [panHandlerName]: PanGestureHandlerEventPayload;\n  [pinchHandlerName]: PinchGestureHandlerEventPayload;\n  [rotationHandlerName]: RotationGestureHandlerEventPayload;\n  [tapHandlerName]: TapGestureHandlerEventPayload;\n}>;\n\nconst handlersDefaultEvents: DefaultEventsMapping = {\n  [flingHandlerName]: {\n    x: 0,\n    y: 0,\n    absoluteX: 0,\n    absoluteY: 0,\n    numberOfPointers: 1,\n  },\n  [forceTouchHandlerName]: {\n    x: 0,\n    y: 0,\n    absoluteX: 0,\n    absoluteY: 0,\n    force: 1,\n    numberOfPointers: 1,\n  },\n  [longPressHandlerName]: {\n    x: 0,\n    y: 0,\n    absoluteX: 0,\n    absoluteY: 0,\n    duration: 100,\n    numberOfPointers: 1,\n  },\n  [nativeViewHandlerName]: {\n    pointerInside: true,\n    numberOfPointers: 1,\n  },\n  [panHandlerName]: {\n    x: 0,\n    y: 0,\n    absoluteX: 0,\n    absoluteY: 0,\n    translationX: 100,\n    translationY: 0,\n    velocityX: 3,\n    velocityY: 0,\n    numberOfPointers: 1,\n    stylusData: undefined,\n  },\n  [pinchHandlerName]: {\n    focalX: 0,\n    focalY: 0,\n    scale: 2,\n    velocity: 1,\n    numberOfPointers: 2,\n  },\n  [rotationHandlerName]: {\n    anchorX: 0,\n    anchorY: 0,\n    rotation: 3.14,\n    velocity: 2,\n    numberOfPointers: 2,\n  },\n  [tapHandlerName]: {\n    x: 0,\n    y: 0,\n    absoluteX: 0,\n    absoluteY: 0,\n    numberOfPointers: 1,\n  },\n};\n\nfunction isGesture(\n  componentOrGesture: ReactTestInstance | GestureType\n): componentOrGesture is GestureType {\n  return componentOrGesture instanceof BaseGesture;\n}\n\ninterface WrappedGestureHandlerTestEvent {\n  nativeEvent: GestureHandlerTestEvent;\n}\nfunction wrapWithNativeEvent(\n  event: GestureHandlerTestEvent\n): WrappedGestureHandlerTestEvent {\n  return { nativeEvent: event };\n}\n\nfunction fillOldStateChanges(\n  previousEvent: GestureHandlerTestEvent | null,\n  currentEvent: Omit<GestureHandlerTestEvent, 'oldState'>\n): GestureHandlerTestEvent {\n  const isFirstEvent = previousEvent === null;\n  if (isFirstEvent) {\n    return {\n      oldState: State.UNDETERMINED,\n      ...currentEvent,\n    } as GestureHandlerTestEvent;\n  }\n\n  const isGestureStateEvent = previousEvent.state !== currentEvent.state;\n  if (isGestureStateEvent) {\n    return {\n      oldState: previousEvent?.state,\n      ...currentEvent,\n    } as GestureHandlerTestEvent;\n  } else {\n    return currentEvent as GestureHandlerTestEvent;\n  }\n}\n\ntype EventWithStates = Partial<\n  Pick<GestureHandlerTestEvent, 'oldState' | 'state'>\n>;\nfunction validateStateTransitions(\n  previousEvent: EventWithStates | null,\n  currentEvent: EventWithStates\n) {\n  function stringify(event: Record<string, unknown> | null) {\n    return JSON.stringify(event, null, 2);\n  }\n  function errorMsgWithBothEvents(description: string) {\n    return `${description}, invalid event: ${stringify(\n      currentEvent\n    )}, previous event: ${stringify(previousEvent)}`;\n  }\n\n  function errorMsgWithCurrentEvent(description: string) {\n    return `${description}, invalid event: ${stringify(currentEvent)}`;\n  }\n\n  invariant(\n    hasProperty(currentEvent, 'state'),\n    errorMsgWithCurrentEvent('every event must have state')\n  );\n\n  const isFirstEvent = previousEvent === null;\n  if (isFirstEvent) {\n    invariant(\n      currentEvent.state === State.BEGAN,\n      errorMsgWithCurrentEvent('first event must have BEGAN state')\n    );\n  }\n\n  if (previousEvent !== null) {\n    if (previousEvent.state !== currentEvent.state) {\n      invariant(\n        hasProperty(currentEvent, 'oldState'),\n        errorMsgWithCurrentEvent(\n          'when state changes, oldState field should be present'\n        )\n      );\n      invariant(\n        currentEvent.oldState === previousEvent.state,\n        errorMsgWithBothEvents(\n          \"when state changes, oldState should be the same as previous event' state\"\n        )\n      );\n    }\n  }\n\n  return currentEvent;\n}\n\ntype EventWithoutStates = Omit<GestureHandlerTestEvent, 'oldState' | 'state'>;\ninterface HandlerInfo {\n  handlerType: HandlerNames;\n  handlerTag: number;\n}\nfunction fillMissingDefaultsFor({\n  handlerType,\n  handlerTag,\n}: HandlerInfo): (\n  event: Partial<GestureHandlerTestEvent>\n) => EventWithoutStates {\n  return (event) => {\n    return {\n      ...handlersDefaultEvents[handlerType],\n      ...event,\n      handlerTag,\n    };\n  };\n}\n\nfunction isDiscreteHandler(handlerType: HandlerNames) {\n  return (\n    handlerType === 'TapGestureHandler' ||\n    handlerType === 'LongPressGestureHandler'\n  );\n}\n\nfunction fillMissingStatesTransitions(\n  events: EventWithoutStates[],\n  isDiscreteHandler: boolean\n): EventWithoutStates[] {\n  type Event = EventWithoutStates | null;\n  const _events = [...events];\n  const lastEvent = _events[_events.length - 1] ?? null;\n  const firstEvent = _events[0] ?? null;\n\n  const shouldDuplicateFirstEvent =\n    !isDiscreteHandler && !hasState(State.BEGAN)(firstEvent);\n  if (shouldDuplicateFirstEvent) {\n    const duplicated = { ...firstEvent, state: State.BEGAN };\n    // @ts-ignore badly typed, property may exist and we don't want to copy it\n    delete duplicated.oldState;\n    _events.unshift(duplicated);\n  }\n\n  const shouldDuplicateLastEvent =\n    !hasState(State.END)(lastEvent) ||\n    !hasState(State.FAILED)(lastEvent) ||\n    !hasState(State.CANCELLED)(lastEvent);\n\n  if (shouldDuplicateLastEvent) {\n    const duplicated = { ...lastEvent, state: State.END };\n    // @ts-ignore badly typed, property may exist and we don't want to copy it\n    delete duplicated.oldState;\n    _events.push(duplicated);\n  }\n\n  function isWithoutState(event: Event) {\n    return event !== null && !hasProperty(event, 'state');\n  }\n  function hasState(state: State) {\n    return (event: Event) => event !== null && event.state === state;\n  }\n  function noEventsLeft(event: Event) {\n    return event === null;\n  }\n\n  function trueFn() {\n    return true;\n  }\n  interface Args {\n    shouldConsumeEvent?: (event: Event) => boolean;\n    shouldTransitionToNextState?: (nextEvent: Event) => boolean;\n  }\n  function fillEventsForCurrentState({\n    shouldConsumeEvent = trueFn,\n    shouldTransitionToNextState = trueFn,\n  }: Args) {\n    function peekCurrentEvent(): Event {\n      return _events[0] ?? null;\n    }\n    function peekNextEvent(): Event {\n      return _events[1] ?? null;\n    }\n    function consumeCurrentEvent() {\n      _events.shift();\n    }\n    const currentEvent = peekCurrentEvent();\n    const nextEvent = peekNextEvent();\n    const currentRequiredState = REQUIRED_EVENTS[currentStateIdx];\n\n    let eventData = {};\n    const shouldUseEvent = shouldConsumeEvent(currentEvent);\n    if (shouldUseEvent) {\n      eventData = currentEvent!;\n      consumeCurrentEvent();\n    }\n    transformedEvents.push({ state: currentRequiredState, ...eventData });\n    if (shouldTransitionToNextState(nextEvent)) {\n      currentStateIdx++;\n    }\n  }\n\n  const REQUIRED_EVENTS = [State.BEGAN, State.ACTIVE, State.END];\n\n  let currentStateIdx = 0;\n  const transformedEvents: EventWithoutStates[] = [];\n  let hasAllStates;\n  let iterations = 0;\n  do {\n    const nextRequiredState = REQUIRED_EVENTS[currentStateIdx];\n    if (nextRequiredState === State.BEGAN) {\n      fillEventsForCurrentState({\n        shouldConsumeEvent: (e: Event) =>\n          isWithoutState(e) || hasState(State.BEGAN)(e),\n      });\n    } else if (nextRequiredState === State.ACTIVE) {\n      const shouldConsumeEvent = (e: Event) =>\n        isWithoutState(e) || hasState(State.ACTIVE)(e);\n      const shouldTransitionToNextState = (nextEvent: Event) =>\n        noEventsLeft(nextEvent) ||\n        hasState(State.END)(nextEvent) ||\n        hasState(State.FAILED)(nextEvent) ||\n        hasState(State.CANCELLED)(nextEvent);\n\n      fillEventsForCurrentState({\n        shouldConsumeEvent,\n        shouldTransitionToNextState,\n      });\n    } else if (nextRequiredState === State.END) {\n      fillEventsForCurrentState({});\n    }\n    hasAllStates = currentStateIdx === REQUIRED_EVENTS.length;\n\n    invariant(\n      iterations++ <= 500,\n      'exceeded max number of iterations, please report a bug in RNGH repository with your test case'\n    );\n  } while (!hasAllStates);\n\n  return transformedEvents;\n}\n\ntype EventEmitter = (\n  eventName: string,\n  args: { nativeEvent: GestureHandlerTestEvent }\n) => void;\ninterface HandlerData {\n  emitEvent: EventEmitter;\n  handlerType: HandlerNames;\n  handlerTag: number;\n  enabled: boolean | undefined;\n}\nfunction getHandlerData(\n  componentOrGesture: ReactTestInstance | GestureType\n): HandlerData {\n  if (isGesture(componentOrGesture)) {\n    const gesture = componentOrGesture;\n    return {\n      emitEvent: (eventName, args) => {\n        DeviceEventEmitter.emit(eventName, args.nativeEvent);\n      },\n      handlerType: gesture.handlerName as HandlerNames,\n      handlerTag: gesture.handlerTag,\n      enabled: gesture.config.enabled,\n    };\n  }\n  const gestureHandlerComponent = componentOrGesture;\n  return {\n    emitEvent: (eventName, args) => {\n      fireEvent(gestureHandlerComponent, eventName, args);\n    },\n    handlerType: gestureHandlerComponent.props.handlerType as HandlerNames,\n    handlerTag: gestureHandlerComponent.props.handlerTag as number,\n    enabled: gestureHandlerComponent.props.enabled,\n  };\n}\ntype AllGestures =\n  | TapGesture\n  | PanGesture\n  | LongPressGesture\n  | RotationGesture\n  | PinchGesture\n  | FlingGesture\n  | ForceTouchGesture\n  | NativeGesture;\n\ntype AllHandlers =\n  | TapGestureHandler\n  | PanGestureHandler\n  | LongPressGestureHandler\n  | RotationGestureHandler\n  | PinchGestureHandler\n  | FlingGestureHandler\n  | ForceTouchGestureHandler\n  | NativeViewGestureHandler;\n\n// prettier-ignore\ntype ClassComponentConstructor<P> = new (props: P) => React.Component<P, any, any>;\n\ntype ExtractPayloadFromProps<T> =\n  T extends BaseGestureHandlerProps<infer TPayload> ? TPayload : never;\n\ntype ExtractConfig<T> =\n  T extends BaseGesture<infer TGesturePayload>\n    ? TGesturePayload\n    : T extends ClassComponentConstructor<infer THandlerProps>\n      ? ExtractPayloadFromProps<THandlerProps>\n      : Record<string, unknown>;\n\nexport function fireGestureHandler<THandler extends AllGestures | AllHandlers>(\n  componentOrGesture: ReactTestInstance | GestureType,\n  eventList: Partial<GestureHandlerTestEvent<ExtractConfig<THandler>>>[] = []\n): void {\n  const { emitEvent, handlerType, handlerTag, enabled } =\n    getHandlerData(componentOrGesture);\n\n  if (enabled === false) {\n    return;\n  }\n\n  let _ = fillMissingStatesTransitions(\n    eventList,\n    isDiscreteHandler(handlerType)\n  );\n  _ = _.map(fillMissingDefaultsFor({ handlerTag, handlerType }));\n  _ = withPrevAndCurrent(_, fillOldStateChanges);\n  _ = withPrevAndCurrent(_, validateStateTransitions);\n  // @ts-ignore TODO\n  _ = _.map(wrapWithNativeEvent);\n\n  const events = _ as unknown as WrappedGestureHandlerTestEvent[];\n\n  const firstEvent = events.shift()!;\n\n  emitEvent('onGestureHandlerStateChange', firstEvent);\n  let lastSentEvent = firstEvent;\n  for (const event of events) {\n    const hasChangedState =\n      lastSentEvent.nativeEvent.state !== event.nativeEvent.state;\n\n    if (hasChangedState) {\n      emitEvent('onGestureHandlerStateChange', event);\n    } else {\n      emitEvent('onGestureHandlerEvent', event);\n    }\n    lastSentEvent = event;\n  }\n}\n\nexport function getByGestureTestId(testID: string) {\n  const handler = findHandlerByTestID(testID);\n  if (handler === null) {\n    throw new Error(`Handler with id: '${testID}' cannot be found`);\n  }\n  return handler;\n}\n"]}